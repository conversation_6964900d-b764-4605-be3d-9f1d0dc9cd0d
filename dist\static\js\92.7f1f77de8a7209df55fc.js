webpackJsonp([92],{"4zxV":function(e,t){},glX5:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n("Aw0F"),a=n("lbHh"),s=n.n(a),c={name:"erp",components:{PageWrap:r.a},props:{menu:{type:Object,default:function(){}}},computed:{src:function(){return this.menu.component+"?token="+s.a.get("employee_token")}}},o={render:function(){var e=this.$createElement,t=this._self._c||e;return t("page-wrap",[t("div",{staticClass:"main-box"},[t("iframe",{staticStyle:{width:"100%",height:"100%"},attrs:{src:this.src,frameborder:"0"}})])])},staticRenderFns:[]};var i=n("VU/8")(c,o,!1,function(e){n("4zxV")},null,null);t.default=i.exports}});