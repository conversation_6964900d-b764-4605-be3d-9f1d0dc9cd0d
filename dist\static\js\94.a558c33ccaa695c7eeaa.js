webpackJsonp([94],{"8m0D":function(e,t){},NHvQ:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=a("mvHQ"),o=a.n(i),s=a("//Fk"),r=a.n(s),l=a("nkgg"),n=a("pI5c"),c=a("VdLP"),p=a("FCNb"),d=a("zL8q"),m={name:"publish",components:{Editor:l.a,Cropper:c.a,YunpanPermission:p.a},data:function(){return{id:this.$route.query.id,loading:!1,fileId:[],formData:{is_top:0,share_is:0,content:"",title:"",type:parseInt(this.$route.query.typeId),pic_id:null,file_ids:"",user_ids:"",source:"",customize:"",outerChain:""},options:[],upload_url:"/api/upload",statement:!1,cover:null,imgWidth:460,imgHeight:340,coverList:[],fileInfo:{},dialogTemplate:!1,dialogPar:{},oldCover:null,enclosureList:[],enclosureFileList:[],permDialogVisible:!1,permdata:[],OrgPersonTree:"",permkey:0,loadingInstance:!1,fileName:"",selectedNames:[],uploadEnclosureLoading:!1,permLoading:!1,sourceList:["腾讯","搜狐","新浪","网易","今日头条","自定义"],rules:{outerChain:[{pattern:/^((https|http|ftp|rtsp|mms)?:\/\/)[^\s]+/,message:"请输入正确的链接",trigger:"blur"}]}}},computed:{showPersonSelector:function(){return[2,3].includes(this.formData.type)},token:function(){return this.$cookies.get("token")||this.$cookies.get("access_token")||this.$store.getters.token},notShare:function(){return![2,3].includes(this.formData.type)}},created:function(){this.id&&this.getDetail(),this.getClassfication()},watch:{"formData.type":{handler:function(e){e&&1!=e&&this.permFile(this.formData.id)},immediate:!0}},methods:{getDetail:function(){var e=this;this.loading=!0,Object(n.x)({id:this.id}).then(function(t){e.loading=!1,e.formData=t.data,e.$set(e.formData,"customize",""),e.cover=t.data.pic,e.oldCover=t.data.pic,t.data.file&&(e.enclosureList=t.data.file),t.data.user_list&&(e.permdata=t.data.user_list.map(function(e){return e.user_id}),e.enclosureList.map(function(e){return e.name=e.file_name})),t.data.source&&(e.sourceList.includes(t.data.source)||(e.$set(e.formData,"customize",t.data.source),e.formData.source="自定义"))}).then(function(){e.loading=!1})},store:function(){var e=this;this.$refs.form.validate(function(t){if(!t)return console.log("error submit!!"),!1;e.loading=!0,e.formData.file_ids=e.fileId.join(","),"自定义"===e.formData.source&&(e.formData.source=e.formData.customize),delete e.formData.customize,Object(n.y)(e.formData).then(function(t){e.loading=!1,200===t.code&&(e.$message.success("操作成功"),e.$router.go(-1))}).then(function(){e.loading=!1})})},back:function(){this.$router.go(-1)},handlecoverSuccess:function(e,t){this.cover=URL.createObjectURL(t.raw),e.data.length&&(this.formData.pic_id=e.data[0])},beforeAvatarUpload:function(e){var t=this,a=this;return new r.a(function(t,i){var o=a.imgWidth,s=a.imgHeight,r=window.URL||window.webkitURL,l=new Image;l.onload=function(){l.width===o&&l.height===s?t():i()},l.src=r.createObjectURL(e)}).then(function(){return e},function(){return t.$message.error("图片长宽不符合规范！"),r.a.reject()})},getClassfication:function(){var e=this;Object(n._12)().then(function(t){200===t.code?e.options=t.data:console.log("信息分类"+t.msg)})},changeCoverFile:function(e,t){var a=this;-1!==e.raw.type.indexOf("image")?(0===this.$refs.coverImg.uploadFiles.length||(this.$refs.coverImg.uploadFiles=[]),this.fileInfo=e,this.$nextTick(function(){a.dialogTitle="基于vue的图片裁剪",a.dialogWidth="500",a.dialogPar={imgUrl:URL.createObjectURL(e.raw),fileInfo:e,autoCropWidth:a.imgWidth,autoCropHeight:a.imgHeight},a.dialogTemplate=!0})):this.$message.warning("请上传图片")},handleExceed:function(e,t){this.$message.warning("最多允许上传3个附件")},handleRemoveEnclosure:function(e,t){this.fileId=t.filter(function(t){return t.file_id!==e.file_id}).map(function(e){return e.file_id}),this.enclosureList=t},submitUpload:function(e){var t=this;this.uploadEnclosureLoading=!0;var a=this,i=e.file,o=new FormData;o.append("token",this.token),o.append("file",i),o.append("file_name",i.name),Object(n._25)(o).then(function(e){t.uploadEnclosureLoading=!1,200===e.code?(a.$message.success("上传成功"),a.enclosureList.push({name:i.name,url:"/"+URL.createObjectURL(i)}),a.fileId.push(e.data[0])):a.$message.error(e.msg)})},handleBeforeUpload:function(e){var t=e.size/1024/1024<10;t||this.$message.error("附件大小不能超过 10MB!");var a=["application/msword","application/vnd.ms-excel","application/vnd.ms-powerpoint","application/pdf","application/vnd.openxmlformats-officedocument.wordprocessingml.document","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","application/vnd.openxmlformats-officedocument.presentationml.presentation"];return-1===a.indexOf(e.type)&&this.$message.error("附件类型只能为word、ppt、excel、pdf格式!"),t&&-1!==a.indexOf(e.type)},emitPar:function(e){this.dialogTemplate=!1;var t=this;if(e&&e.imgUrl){this.fileInfo.raw=e.fileData;var a=window.URL||window.webkitURL;(new Image).src=a.createObjectURL(this.fileInfo.raw);var i=new FormData;i.append("files[]",t.fileInfo.raw),Object(n._26)(i).then(function(e){t.handlecoverSuccess(e,t.fileInfo)})}this.logoList=[]},cancel:function(){this.cover=this.oldCover,this.fileInfo={},this.dialogTemplate=!1},parentFn:function(e){this.permdata=e},permFile:function(e){var t=this,a=this;this.permFileId=e,this.loadingInstance=d.Loading.service({text:"加载中",target:".table"}),Object(n._1)().then(function(e){t.permLoading=!1,200===e.code?(a.OrgPersonTree=e.data[0],console.log(222)):console.log(e),t.loadingInstance.close()}),e&&(Object(n.Y)({file_id:e}).then(function(e){200===e.code&&(a.permdata=e.data.list)}),a.permkey=e)},permSubmit:function(){this.selectedNames=this.$refs.permission.$refs.vueSelectl.curLists,this.selectedNames=this.selectedNames.map(function(e){return{user_id:e.id,company_id:e.company_id}}),this.formData.user_ids=o()(this.selectedNames),console.log(this.formData.user_ids),this.permDialogVisible=!1}}},u={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-card",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"box-card"},[a("div",{staticClass:"header_title"},[a("h1",[e._v(e._s(e.id?"修改":"新增")+"企业消息")])]),e._v(" "),a("el-form",{ref:"form",attrs:{model:e.formData,rules:e.rules,"label-width":"90px","label-position":"left"}},[a("el-form-item",{attrs:{prop:"type",label:"分类"}},[a("el-select",{attrs:{placeholder:"请选择"},model:{value:e.formData.type,callback:function(t){e.$set(e.formData,"type",t)},expression:"formData.type"}},e._l(e.options,function(e){return a("el-option",{key:e.id,attrs:{label:e.title,value:e.id}})}),1)],1),e._v(" "),a("el-form-item",{attrs:{prop:"title",label:"标题"}},[a("el-col",{attrs:{span:12}},[a("el-input",{directives:[{name:"filter-special-char",rawName:"v-filter-special-char"}],attrs:{placeholder:"请输入消息标题",clearable:""},model:{value:e.formData.title,callback:function(t){e.$set(e.formData,"title",t)},expression:"formData.title"}})],1)],1),e._v(" "),[a("el-form-item",{attrs:{prop:"source",label:"消息来源"}},[a("el-col",{attrs:{span:12}},[a("el-select",{attrs:{span:12},model:{value:e.formData.source,callback:function(t){e.$set(e.formData,"source",t)},expression:"formData.source"}},e._l(e.sourceList,function(e){return a("el-option",{key:e,attrs:{label:e,value:e}})}),1)],1)],1),e._v(" "),"自定义"===e.formData.source?a("el-form-item",{attrs:{prop:"customize",label:"自定义来源"}},[a("el-col",{attrs:{span:12}},[a("el-input",{directives:[{name:"filter-special-char",rawName:"v-filter-special-char"}],attrs:{placeholder:"请输入自定义消息来源"},model:{value:e.formData.customize,callback:function(t){e.$set(e.formData,"customize",t)},expression:"formData.customize"}})],1)],1):e._e(),e._v(" "),a("el-form-item",{attrs:{label:"新闻外链",prop:"outerChain"}},[a("el-col",{attrs:{span:12}},[a("el-input",{attrs:{placeholder:"请输入新闻外链"},model:{value:e.formData.outerChain,callback:function(t){e.$set(e.formData,"outerChain",t)},expression:"formData.outerChain"}})],1)],1)],e._v(" "),a("el-form-item",{directives:[{name:"show",rawName:"v-show",value:e.notShare,expression:"notShare"}],attrs:{prop:"share_is",label:"分享"}},[a("el-switch",{attrs:{"active-color":"#13ce66","inactive-color":"#ff4949","active-value":1,"inactive-value":0},model:{value:e.formData.share_is,callback:function(t){e.$set(e.formData,"share_is",t)},expression:"formData.share_is"}}),e._v(" "),a("div",{staticClass:"el-upload__tip",staticStyle:{display:"inline","margin-left":"20px"}},[e._v("开启之后员工可以分享给外部人员浏览")])],1),e._v(" "),a("el-form-item",{attrs:{prop:"is_top",label:"置顶"}},[a("el-switch",{attrs:{"active-value":1,"inactive-value":0,"active-color":"#13ce66","inactive-color":"#ff4949"},model:{value:e.formData.is_top,callback:function(t){e.$set(e.formData,"is_top",t)},expression:"formData.is_top"}})],1),e._v(" "),a("el-form-item",{attrs:{prop:"pic_id",label:"封面图"}},[a("el-upload",{ref:"coverImg",staticClass:"upload-demo",attrs:{accept:"image/*",name:"files[]",data:{token:e.token},action:"","file-list":e.coverList,"auto-upload":!1,multiple:!1,"list-type":"picture-card","show-file-list":!1,"on-change":e.changeCoverFile}},[e.cover?a("el-image",{staticClass:"card",staticStyle:{width:"100%",height:"100%"},attrs:{src:e.cover}}):a("i",{staticClass:"el-icon-plus avatar-uploader-icon"}),e._v(" "),a("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[e._v("请传入"+e._s(e.imgWidth)+" * "+e._s(e.imgHeight)+"大小的图片")]),e._v(" "),a("input",{staticStyle:{display:"none"},domProps:{value:e.formData.pic_id}})],1)],1),e._v(" "),[a("el-form-item",{attrs:{prop:"file_ids",label:"附件"}},[a("el-upload",{ref:"enclosure",staticClass:"upload-demo",attrs:{accept:"application/msword,application/vnd.ms-powerpoint,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/pdf",name:"file",data:{token:e.token,file_name:e.fileName},action:"","file-list":e.enclosureList,"auto-upload":!0,multiple:!1,"show-file-list":!0,"http-request":e.submitUpload,limit:3,"on-exceed":e.handleExceed,"before-upload":e.handleBeforeUpload,"before-remove":e.handleRemoveEnclosure}},[a("div",{staticStyle:{display:"flex","align-items":"center"}},[a("el-button",{directives:[{name:"loading",rawName:"v-loading",value:e.uploadEnclosureLoading,expression:"uploadEnclosureLoading"}],staticStyle:{"margin-right":"10px"},attrs:{size:"small",type:"primary"}},[e._v("点击上传")]),e._v(" "),a("div",{staticClass:"el-upload__tip",staticStyle:{"margin-top":"0"},attrs:{slot:"tip"},slot:"tip"},[e._v("仅支持上传pdf、word、excel、ppt格式的文件")])],1)]),e._v(" "),a("input",{staticStyle:{display:"none"},attrs:{type:"text"},domProps:{value:e.formData.file_ids}})],1)],e._v(" "),[a("el-form-item",{attrs:{label:"通知人员"}},[a("div",{staticStyle:{display:"flex","align-items":"center"}},[a("el-button",{directives:[{name:"loading",rawName:"v-loading",value:e.permLoading,expression:"permLoading"}],staticStyle:{"margin-right":"10px"},attrs:{type:"primary",size:"small"},on:{click:function(t){e.permDialogVisible=!0}}},[e._v("选择人员")]),e._v(" "),a("div",{staticClass:"el-upload__tip",staticStyle:{"margin-top":"0"},attrs:{slot:"tip"},slot:"tip"},[e._v("已选择"+e._s(e.permdata.length)+"人")])],1),e._v(" "),a("ul",{staticStyle:{overflow:"hidden","margin-left":"50px",display:"none"}},e._l(e.selectedNames,function(t){return a("li",{key:t.id,staticStyle:{float:"left","margin-left":"10px","margin-bottom":"10px"}},[e._v(e._s(t.name))])}),0)])],e._v(" "),a("el-form-item",{staticStyle:{width:"100%"},attrs:{prop:"content",label:"消息内容"}},[a("el-col",{attrs:{span:24}},[a("Editor",{attrs:{content:e.formData.content},on:{"update:content":function(t){return e.$set(e.formData,"content",t)}}})],1)],1)],2),e._v(" "),a("el-button",{on:{click:e.back}},[e._v("返回")]),e._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:e.store}},[e._v(e._s(e.id?"信息修改":"信息发布")+" ")])],1),e._v(" "),a("el-dialog",{attrs:{title:"图片裁剪",visible:e.dialogTemplate,width:"800px","append-to-body":"","before-close":e.cancel},on:{"update:visible":function(t){e.dialogTemplate=t}}},[a("Cropper",{attrs:{"dialog-par":e.dialogPar},on:{emitPar:e.emitPar,cancel:e.cancel}})],1),e._v(" "),a("el-dialog",{attrs:{title:"选择通知人员",visible:e.permDialogVisible,width:"50%"},on:{"update:visible":function(t){e.permDialogVisible=t}}},[a("yunpan-permission",{key:e.permkey,ref:"permission",attrs:{inidata:e.permdata,OrgPersonTree:e.OrgPersonTree},on:{childFn:e.parentFn}}),e._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{size:"small"},on:{click:function(t){e.permDialogVisible=!1}}},[e._v("取 消")]),e._v(" "),a("el-button",{attrs:{type:"primary",size:"small"},on:{click:e.permSubmit}},[e._v("确 定")])],1)],1)],1)},staticRenderFns:[]};var f=a("VU/8")(m,u,!1,function(e){a("8m0D")},"data-v-275aa23d",null);t.default=f.exports}});