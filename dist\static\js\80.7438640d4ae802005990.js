webpackJsonp([80],{LTC1:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=a("cMGX"),i=a("pI5c"),l={name:"order",components:{Pagination:n.a},data:function(){return{tableLoading:!1,tableData:[],total:0,queryList:{page:1,perPage:10,refund:!0}}},created:function(){this.getList()},methods:{getList:function(){var t=this;this.tableLoading=!0,Object(i.f)(this.queryList).then(function(e){t.tableLoading=!1,t.tableData=e.data}).catch(function(){t.tableLoading=!1})},recede:function(t){this.$router.push({path:"/console/orderRecede/detail",query:{id:t.id}})}}},r={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-container"},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[t._v("退订管理")])]),t._v(" "),a("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:t.queryList,size:"small"}},[a("el-form-item",{attrs:{label:"产品名称"}},[a("el-input",{attrs:{placeholder:"产品名称",clearable:""},model:{value:t.queryList.name,callback:function(e){t.$set(t.queryList,"name",e)},expression:"queryList.name"}})],1),t._v(" "),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:t.getList}},[t._v("搜索")])],1)],1),t._v(" "),a("el-divider"),t._v(" "),a("div",{staticClass:"table",staticStyle:{"margin-top":"50px"}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.tableLoading,expression:"tableLoading"}],staticStyle:{width:"100%"},attrs:{data:t.tableData,stripe:""}},[a("el-table-column",{attrs:{prop:"name",label:"产品名称"}}),t._v(" "),a("el-table-column",{attrs:{prop:"package",label:"产品配置"}}),t._v(" "),a("el-table-column",{attrs:{prop:"price",label:"续费价格"}}),t._v(" "),a("el-table-column",{attrs:{prop:"expiration_at",label:"到期时间"}}),t._v(" "),a("el-table-column",{attrs:{label:"倒计时(天)"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.remaining_days))]),t._v(" "),e.row.remaining_days<10?a("span",[t._v("即将到期，请及时续费")]):t._e()]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"操作"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{type:"primary"},on:{click:function(a){return t.recede(e.row)}}},[t._v("退订申请")])]}}])})],1)],1),t._v(" "),a("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total>0"}],staticStyle:{"text-align":"center"},attrs:{total:t.total,page:t.queryList.page,limit:t.queryList.perPage},on:{"update:page":function(e){return t.$set(t.queryList,"page",e)},"update:limit":function(e){return t.$set(t.queryList,"perPage",e)},pagination:t.getList}})],1)],1)},staticRenderFns:[]};var s=a("VU/8")(l,r,!1,function(t){a("bMqo")},"data-v-5d184d20",null);e.default=s.exports},bMqo:function(t,e){}});