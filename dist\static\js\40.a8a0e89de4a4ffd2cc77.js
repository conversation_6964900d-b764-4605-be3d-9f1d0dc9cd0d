webpackJsonp([40],{"2F+z":function(a,e){},"ft/7":function(a,e){},qfQ5:function(a,e){},rckB:function(a,e,t){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var i=t("pI5c"),r=t("Wxq9"),s=t("woOf"),o=t.n(s),l=t("mvHQ"),n=t.n(l),c=t("VdLP"),m={name:"amendPwd",props:{dialogVisible:{required:!1,type:Boolean,default:function(){return!1}},formData:{required:!1,type:Object,default:function(){return{}}},province_ids:{required:!1,type:Array,default:function(){return[]}}},components:{Cropper:c.a},watch:{formData:function(a,e){this.avatar=a.avatar}},data:function(){return{imgWidth:200,imgHeight:200,avatarList:[],dialogPar:{},dialogTemplate:!1,fileInfo:{},showProvinceError:!1,provinceAndCityData:r.provinceAndCityData,upload_url:"/api/upload",btn_loading:!1,avatar:"",provinceArr:[],rules:{name:[{required:!0,message:"请输入姓名",trigger:"blur"},{min:1,max:10,message:"姓名不能超过10个字符",trigger:"blur"}],avatar:[{required:!1,message:"请上传头像",trigger:"blur"}],sex:[{required:!0,message:"请选择性别",trigger:"blur"}],phone:[{required:!0,message:"请填写手机号",trigger:"blur"}],email:[{type:"email",required:!0,message:"请输入合法邮箱",trigger:"blur"}]}}},computed:{token:function(){return this.$cookies.get("token")||this.$cookies.get("access_token")||this.$store.getters.token}},created:function(){this.provinceArr=JSON.parse(n()(this.province_ids))},methods:{handleAvatarSuccess:function(a,e){200===a.code&&(this.$listeners.changeAvatar(a.data[0]),this.avatar=URL.createObjectURL(e.raw))},addressChange:function(a){this.formData.province=r.CodeToText[a[0]],this.formData.city=r.CodeToText[a[1]],this.$listeners.provinceArr(a),this.showProvinceError=!0},userUpdate:function(){var a=this;this.btn_loading=!0,this.showProvinceError=!0,this.$refs.formData.validate(function(e){if(!e)return a.$message.warning("请先将表单填写完整"),a.btn_loading=!1,!1;var t=JSON.parse(n()(o()({},a.formData)));t.avatar&&t.avatar.length>20&&delete t.avatar,parseInt(t.sex)||(t.sex="男"===t.sex?1:2),Number(t.avatar)||delete t.avatar,delete t.create_time,Object(i._33)(t).then(function(e){200===e.code?(a.$emit("refresh"),a.close(),a.$message.success("修改成功"),window.location.reload()):a.$message.error("修改失败")}).finally(function(){a.btn_loading=!1})})},close:function(){this.$emit("closepop"),this.$emit("refresh")},changeAvatarFile:function(a,e){var t=this;0===this.$refs.avatarImg.uploadFiles.length||(this.$refs.avatarImg.uploadFiles=[]),this.fileInfo=a,this.$nextTick(function(){t.dialogPar={imgUrl:URL.createObjectURL(a.raw),fileInfo:a,autoCropWidth:t.imgWidth,autoCropHeight:t.imgHeight},t.dialogTemplate=!0})},emitPar:function(a){var e=this;if(this.dialogTemplate=!1,a&&a.imgUrl){this.fileInfo.raw=a.fileData;var t=new FormData;t.append("files[]",this.fileInfo.raw),Object(i._26)(t).then(function(a){e.handleAvatarSuccess(a,e.fileInfo)})}this.avatarList=[]},cancel:function(){this.avatar=this.formData.avatar,this.fileInfo={},this.dialogTemplate=!1}}},d={render:function(){var a=this,e=a.$createElement,t=a._self._c||e;return t("el-dialog",{attrs:{title:"基本信息",visible:a.dialogVisible,width:"600px","before-close":a.close},on:{"update:visible":function(e){a.dialogVisible=e}}},[t("div",[t("el-form",{ref:"formData",staticClass:"form",attrs:{"label-position":"left","label-width":"100px",inline:!0,model:a.formData,size:"small",rules:a.rules}},[t("div",{staticClass:"container"},[t("el-form-item",{attrs:{label:"头像",prop:"avatar"}},[t("el-upload",{ref:"avatarImg",staticClass:"upload-demo",attrs:{accept:"image/*",name:"files[]",data:{token:a.token},action:"","file-list":a.avatarList,"auto-upload":!1,multiple:!1,"list-type":"picture-card","show-file-list":!1,"on-change":a.changeAvatarFile}},[a.formData.avatar?t("el-image",{staticClass:"avatar",attrs:{fit:"cover",src:a.avatar}}):t("i",{staticClass:"el-icon-plus avatar-uploader-icon"}),a._v(" "),t("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[a._v("请传入"+a._s(a.imgWidth)+"* "+a._s(a.imgHeight)+"大小的图片")])],1)],1),a._v(" "),t("el-form-item",{attrs:{label:"姓名",prop:"name"}},[t("div",{staticClass:"item"},[t("el-input",{attrs:{placeholder:"请输入姓名",clearable:""},model:{value:a.formData.name,callback:function(e){a.$set(a.formData,"name",e)},expression:"formData.name"}})],1)]),a._v(" "),t("el-form-item",{attrs:{label:"性别",prop:"sex"}},[t("el-radio",{attrs:{label:1},model:{value:a.formData.sex,callback:function(e){a.$set(a.formData,"sex",e)},expression:"formData.sex"}},[a._v("男")]),a._v(" "),t("el-radio",{attrs:{label:2},model:{value:a.formData.sex,callback:function(e){a.$set(a.formData,"sex",e)},expression:"formData.sex"}},[a._v("女")])],1),a._v(" "),t("el-form-item",{attrs:{label:"手机号码",prop:"phone"}},[t("div",{staticClass:"item"},[t("el-input",{attrs:{disabled:!0,value:a._f("phoneEncryption")(a.formData.phone),placeholder:"请输入手机号码",clearable:""}})],1)]),a._v(" "),t("el-form-item",{attrs:{label:"注册邮箱",prop:"email"}},[t("div",{staticClass:"item"},[t("el-input",{attrs:{placeholder:"请输入邮箱",clearable:""},model:{value:a.formData.email,callback:function(e){a.$set(a.formData,"email",e)},expression:"formData.email"}})],1)]),a._v(" "),t("el-form-item",{attrs:{label:"注册时间"}},[t("p",[a._v(a._s(a.formData.created_at))])])],1),a._v(" "),t("el-button",{staticStyle:{"margin-top":"50px",width:"120px"},attrs:{type:"primary",loading:a.btn_loading},on:{click:a.userUpdate}},[a._v("保存")])],1),a._v(" "),t("el-dialog",{attrs:{title:"图片裁剪",visible:a.dialogTemplate,width:"800px","append-to-body":"","before-close":a.cancel},on:{"update:visible":function(e){a.dialogTemplate=e}}},[t("Cropper",{attrs:{"dialog-par":a.dialogPar},on:{emitPar:a.emitPar,cancel:a.cancel}})],1)],1)])},staticRenderFns:[]};var f={name:"index",components:{detailPersonal:t("VU/8")(m,d,!1,function(a){t("2F+z")},"data-v-272a897b",null).exports},data:function(){return{provinceAndCityData:r.provinceAndCityData,upload_url:"/api/upload",formData:{is_certified:0},avatar:"",province_ids:[],company:"",tableLoading:!1,dialogPersonalVisible:!1}},created:function(){this.userInfo(),this.company=this.$store.state.user&&this.$store.state.user.company?this.$store.state.user.company.name:""},methods:{handlePreview:function(a){},userInfo:function(){var a=this;this.tableLoading=!0,Object(i._31)().then(function(e){a.tableLoading=!1,a.company=e.data.companyInfo.enterprise_name;var t=r.TextToCode[e.data.province];if(t){var i=t[e.data.city];a.province_ids=[t.code,i.code]}a.formData=e.data.memberInfo,a.formData.sex="男"===e.data.memberInfo.sex?1:2,a.formData.created_at=e.data.memberInfo.create_time,a.formData.avatar=e.data.memberInfo.avatar,a.avatar=e.data.memberInfo.avatar,a.$store.state.user=e.data})},provinceArr:function(a){this.province_ids=a},changeAvatar:function(a){this.formData.avatar=""+a}}},v={render:function(){var a=this,e=a.$createElement,i=a._self._c||e;return i("div",{staticClass:"app-container"},[i("el-card",{staticClass:"box-card"},[i("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[i("span",[a._v("基本信息")])]),a._v(" "),0===a.formData.is_certified?i("el-alert",{attrs:{title:"您的个人信息尚未完善，请完善个人信息。",type:"warning"}}):a._e(),a._v(" "),i("el-form",{directives:[{name:"loading",rawName:"v-loading",value:a.tableLoading,expression:"tableLoading"}],staticClass:"demo-form-inline",attrs:{inline:!0,model:a.formData}},[i("el-row",[i("el-col",[i("div",{staticClass:"container"},[i("div",{staticClass:"item",staticStyle:{"margin-bottom":"10px",display:"flex","align-items":"center"}},[i("img",{staticStyle:{"margin-right":"10px"},attrs:{src:t("zGWJ")}}),a._v(" "),i("h1",[a._v("个人信息")]),a._v(" "),i("el-button",{attrs:{type:"text"},on:{click:function(e){a.dialogPersonalVisible=!0}}},[a._v("修改")])],1),a._v(" "),i("el-form-item",{attrs:{label:"头像"}},[i("el-image",{staticClass:"avatar",attrs:{fit:"contain",src:a.avatar}})],1),a._v(" "),i("el-form-item",{attrs:{label:"姓名"}},[i("div",{staticClass:"item"},[i("p",{staticStyle:{"white-space":"inherit"}},[a._v("\n                  "+a._s(a.formData.name)+" "),1===a.formData.is_certified?i("el-tag",{attrs:{type:"success",size:"small"}},[a._v("已实名")]):i("el-tag",{attrs:{type:"danger",size:"small"}},[a._v("未实名")])],1)])]),a._v(" "),i("el-form-item",{attrs:{label:"性别"}},[i("el-radio",{attrs:{disabled:!0,label:1},model:{value:a.formData.sex,callback:function(e){a.$set(a.formData,"sex",e)},expression:"formData.sex"}},[a._v("男")]),a._v(" "),i("el-radio",{attrs:{disabled:!0,label:2},model:{value:a.formData.sex,callback:function(e){a.$set(a.formData,"sex",e)},expression:"formData.sex"}},[a._v("女")])],1),a._v(" "),i("el-form-item",{attrs:{label:"手机号码"}},[i("div",{staticClass:"item"},[i("p",[a._v(a._s(a._f("phoneEncryption")(a.formData.phone)))])])]),a._v(" "),i("el-form-item",{attrs:{label:"注册邮箱"}},[i("div",{staticClass:"item"},[i("p",[a._v(a._s(a.formData.email))])])]),a._v(" "),i("el-form-item",{attrs:{label:"注册时间"}},[i("p",[a._v(a._s(a.formData.created_at))])])],1)])],1)],1)],1),a._v(" "),i("detail-personal",{attrs:{dialogVisible:a.dialogPersonalVisible,formData:a.formData,province_ids:a.province_ids},on:{closepop:function(e){a.dialogPersonalVisible=!1},refresh:function(e){return a.userInfo()},provinceArr:a.provinceArr,changeAvatar:a.changeAvatar}})],1)},staticRenderFns:[]};var p=t("VU/8")(f,v,!1,function(a){t("qfQ5"),t("ft/7")},"data-v-39db4720",null);e.default=p.exports},zGWJ:function(a,e){a.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAIAAACQkWg2AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyVpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDYuMC1jMDAyIDc5LjE2NDQ2MCwgMjAyMC8wNS8xMi0xNjowNDoxNyAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIDIxLjIgKE1hY2ludG9zaCkiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6NkYwMDNCQ0VBN0Y3MTFFQkI5MjU5MTcwOEVEMkIzRUUiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6NkYwMDNCQ0ZBN0Y3MTFFQkI5MjU5MTcwOEVEMkIzRUUiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDo2RjAwM0JDQ0E3RjcxMUVCQjkyNTkxNzA4RUQyQjNFRSIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDo2RjAwM0JDREE3RjcxMUVCQjkyNTkxNzA4RUQyQjNFRSIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/Pm1n/OQAAAFsSURBVHjaYvz//z8DDGy5ybD8MgMTI4j96y+DjzpDrD4DOvgPAwvP/3eY93/vvf8/fv//8ef/0Uf/Hef/7zz8Hw1ANTz8AFJ94zWK3Juv/x3n/b/wHEWQCWLPjtsMJtIM6iIolgtzMdgpMGy4gSII1fDtN4M4NwMm4GVj+PYLmwZjKYbTT0Ha0MDV1wy64tg02MqD3PPoA4rc7LMMH34wBGmhCDIiB+uffwwXXjC8/srw8APDuecM338ztDgzyAvg0LDqCsPBB6BI4Odg4GRlsJBhcFbC4iuohpytDM8+MxRagtwGAcAYvPuO4S/MemA8inCBFLAAOaU7GX78YVgXgTDm7z8GFiYGCR4GNmaEBiEuEINl912GSy8Zdsah2MvMBLIKqA3uP2ByYQVrZllxmSHZCN2hv/8yzD7D8Ak1BoQ4GSptGRiCV/z//PM/8YBFjp+Bhw2LDS2HGN58RfGDKDdDgyNqPBADAAIMAKEI7RHfSlHfAAAAAElFTkSuQmCC"}});