webpackJsonp([96],{"/OSQ":function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var l=a("ehIy"),n={name:"getInvoice",data:function(){return{tableLoading:!1,tableData:[],formQuery:{},multipleSelection:[],amount:0}},created:function(){this.getList()},methods:{getList:function(){var t=this;this.tableLoading=!0,Object(l.m)().then(function(e){t.tableLoading=!1,t.tableData=e.data}).catch(function(){t.tableLoading=!1})},handleSelectionChange:function(t){this.multipleSelection=t;var e=0;t.forEach(function(t){e+=parseFloat(t.amount)}),this.amount=e},push:function(){if(0===this.multipleSelection.length)return this.$message.error("至少选择一个发票");var t=[];this.multipleSelection.forEach(function(e){t.push(e.id)}),this.$router.push({path:"/console/invoice/apply",query:{info:this.$G7.encrypt({ids:t.join(","),amount:this.amount})}})},cancel:function(){this.$router.go(-1)}}},i={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[t._v("开票记录")])]),t._v(" "),a("div",{staticStyle:{"margin-bottom":"30px"}},[a("p",{staticStyle:{color:"red"}},[t._v("1.消费完成的订单请在消费日起30天内索取发票")]),t._v(" "),a("p",{staticStyle:{display:"inline-block"}},[t._v("2.一张发票只能有一个税率。当开具发票的税率有多个时，系统会自动生成多张发票。发票内容和发票税率的详细信息请参见")]),t._v(" "),a("a",[t._v("发票内容和发票税率说明")])]),t._v(" "),a("el-form",{staticClass:"demo-form-inline",staticStyle:{display:"none"},attrs:{inline:!0,model:t.formQuery,size:"small"}},[a("el-form-item",{attrs:{label:"消费时间"}},[a("el-date-picker",{attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:t.formQuery.date,callback:function(e){t.$set(t.formQuery,"date",e)},expression:"formQuery.date"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"订单号"}},[a("el-input",{attrs:{placeholder:"请输入订单号"}})],1),t._v(" "),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:t.getList}},[t._v("搜索")])],1)],1),t._v(" "),a("el-divider",{staticStyle:{margin:"0"}}),t._v(" "),a("div",{staticClass:"table",staticStyle:{"margin-top":"20px"}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.tableLoading,expression:"tableLoading"}],staticStyle:{width:"100%"},attrs:{data:t.tableData,stripe:""},on:{"selection-change":t.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55"}}),t._v(" "),a("el-table-column",{attrs:{prop:"order_no",label:"订单号"}}),t._v(" "),a("el-table-column",{attrs:{prop:"title",label:"名称"}}),t._v(" "),a("el-table-column",{attrs:{prop:"created_at",label:"消费时间"}}),t._v(" "),a("el-table-column",{attrs:{prop:"amount",label:"实付金额"}})],1)],1),t._v(" "),a("div",{staticClass:"footer"},[a("p",[t._v("待开票金额:")]),t._v(" "),a("p",[t._v("￥"+t._s(t.amount))]),t._v(" "),a("div",[a("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.push()}}},[t._v("下一步")]),t._v(" "),a("el-button",{attrs:{type:"warning"},on:{click:t.cancel}},[t._v("取消")])],1)])],1)},staticRenderFns:[]};var o=a("VU/8")(n,i,!1,function(t){a("KYdA")},"data-v-1d18e364",null);e.default=o.exports},KYdA:function(t,e){}});