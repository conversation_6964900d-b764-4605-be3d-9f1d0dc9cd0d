webpackJsonp([93],{"1VyR":function(a,t,e){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var l=e("cMGX"),n=e("pI5c"),o={name:"order",components:{Pagination:l.a},data:function(){return{tableLoading:!1,radio:"",agree:!1}},created:function(){this.no=this.$route.query.no},methods:{getDetail:function(){var a=this;this.tableLoading=!0,Object(n._15)({no:this.no}).then(function(t){a.tableLoading=!1,a.data=t.data}).catch(function(){a.tableLoading=!1})},showDetail:function(){}}},r={render:function(){var a=this,t=a.$createElement,e=a._self._c||t;return e("div",{staticClass:"app-container"},[e("el-card",{staticClass:"box-card",attrs:{"v-loading":a.tableLoading}},[e("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[e("span",[a._v("退订申请")])]),a._v(" "),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:a.tableLoading,expression:"tableLoading"}],staticStyle:{width:"100%"},attrs:{data:a.tableData,stripe:""}},[e("el-table-column",{attrs:{prop:"no",label:"订单编号"}}),a._v(" "),e("el-table-column",{attrs:{prop:"type",label:"费用类型"}}),a._v(" "),e("el-table-column",{attrs:{prop:"title",label:"产品名称"}}),a._v(" "),e("el-table-column",{attrs:{prop:"desc",label:"产品配置"}}),a._v(" "),e("el-table-column",{attrs:{label:"购买方式"}},[e("span",[a._v("周期购买")])]),a._v(" "),e("el-table-column",{attrs:{prop:"month",label:"购买时长"},scopedSlots:a._u([{key:"default",fn:function(t){return[e("span",[a._v(a._s(t.row.month)+"年")])]}}])}),a._v(" "),e("el-table-column",{attrs:{prop:"money",label:"购买价格"}}),a._v(" "),e("el-table-column",{attrs:{prop:"time",label:"下单时间"}}),a._v(" "),e("el-table-column",{attrs:{prop:"status",label:"支付状态"}}),a._v(" "),e("el-table-column",{attrs:{label:"操作"},scopedSlots:a._u([{key:"default",fn:function(t){return[e("el-button",{attrs:{type:"primary"},on:{click:function(e){return a.showDetail(t.row)}}},[a._v("详情")])]}}])})],1),a._v(" "),e("h2",[a._v("请选择退订原因")]),a._v(" "),e("el-radio-group",{model:{value:a.radio,callback:function(t){a.radio=t},expression:"radio"}},[e("el-radio",{attrs:{label:3}},[a._v("备选项")]),a._v(" "),e("el-radio",{attrs:{label:6}},[a._v("备选项")]),a._v(" "),e("el-radio",{attrs:{label:10}},[a._v("其他")])],1),a._v(" "),10===a.radio?e("el-input",{staticStyle:{display:"block",width:"300px"},attrs:{type:"textarea",rows:3,placeholder:"请输入退款理由",maxlength:"50","show-word-limit":""},model:{value:a.textarea,callback:function(t){a.textarea=t},expression:"textarea"}}):a._e(),a._v(" "),e("div",{staticClass:"amount"},[e("div",[e("span",[a._v("实付金额:")]),a._v(" "),e("span",[a._v("0000")])]),a._v(" "),e("div",[e("span",[a._v("已消费金额:")]),a._v(" "),e("span",[a._v("0000")])]),a._v(" "),e("div",[e("span",[a._v("退订金额:")]),a._v(" "),e("span",[a._v("0000")])]),a._v(" "),e("div",[e("span",[a._v("退还金额:")]),a._v(" "),e("span",[a._v("0000")])])]),a._v(" "),e("div",{staticClass:"agree"},[e("p",[a._v("*")]),a._v(" "),e("el-checkbox",{model:{value:a.agree,callback:function(t){a.agree=t},expression:"agree"}},[a._v("我已确认本次退订金额和相关费用")])],1),a._v(" "),e("el-button",{staticStyle:{"margin-top":"30px"},attrs:{disabled:!a.agree,type:"primary"}},[a._v("退订")])],1)],1)},staticRenderFns:[]};var s=e("VU/8")(o,r,!1,function(a){e("L1Z1")},"data-v-296f7bea",null);t.default=s.exports},L1Z1:function(a,t){}});