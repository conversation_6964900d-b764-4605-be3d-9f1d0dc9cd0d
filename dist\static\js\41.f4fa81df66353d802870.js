webpackJsonp([41],{"P+wk":function(e,t){},SS3P:function(e,t){},ZO2o:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var a={name:"VideoPlayer",props:{visible:{type:Boolean,default:!1},videoUrl:{type:String,required:!0},title:{type:String,default:"视频播放"}},watch:{visible:function(e){e?(this.playVideo(),this.addKeyboardListener()):(this.stopVideo(),this.removeKeyboardListener())}},methods:{playVideo:function(){var e=this;this.$nextTick(function(){e.$refs.videoPlayer&&e.$refs.videoPlayer.play().catch(function(e){console.log("视频自动播放失败:",e)})})},stopVideo:function(){this.$refs.videoPlayer&&(this.$refs.videoPlayer.pause(),this.$refs.videoPlayer.currentTime=0)},closeModal:function(){this.$emit("close")},handleModalClick:function(){this.closeModal()},handleKeydown:function(e){"Escape"===e.key&&this.visible&&this.closeModal()},addKeyboardListener:function(){document.addEventListener("keydown",this.handleKeydown)},removeKeyboardListener:function(){document.removeEventListener("keydown",this.handleKeydown)},onVideoLoadStart:function(){console.log("视频开始加载"),this.$emit("loadstart")},onVideoCanPlay:function(){console.log("视频可以播放"),this.$emit("canplay")},onVideoError:function(e){console.error("视频播放错误:",e),this.$emit("error",e),alert("视频加载失败，请检查网络连接或稍后重试")}},beforeDestroy:function(){this.removeKeyboardListener()}},n={render:function(){var e=this,t=e.$createElement,o=e._self._c||t;return e.visible?o("div",{staticClass:"video-modal",on:{click:e.handleModalClick}},[o("div",{staticClass:"video-modal-content",on:{click:function(e){e.stopPropagation()}}},[o("div",{staticClass:"video-modal-header"},[o("h3",[e._v(e._s(e.title))]),e._v(" "),o("button",{staticClass:"close-btn",on:{click:e.closeModal}},[e._v("×")])]),e._v(" "),o("div",{staticClass:"video-wrapper"},[o("video",{ref:"videoPlayer",attrs:{src:e.videoUrl,controls:"",autoplay:""},on:{loadstart:e.onVideoLoadStart,canplay:e.onVideoCanPlay,error:e.onVideoError}},[e._v("\n        您的浏览器不支持视频播放。\n      ")])])])]):e._e()},staticRenderFns:[]};var i={name:"HeaderBanner",components:{VideoPlayer:o("VU/8")(a,n,!1,function(e){o("SS3P")},"data-v-25bdff1a",null).exports},data:function(){return{showVideoModal:!1,videoUrl:"https://zcloud.obs.cn-north-4.myhuaweicloud.com/com_pan/company_80895/20250628/2025062814123150491.mp4"}},methods:{playVideo:function(){this.showVideoModal=!0},closeVideoModal:function(){this.showVideoModal=!1},onVideoLoadStart:function(){console.log("HeaderBanner: 视频开始加载")},onVideoCanPlay:function(){console.log("HeaderBanner: 视频可以播放")},onVideoError:function(e){console.error("HeaderBanner: 视频播放错误:",e)}}},s={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"banner-wrapper"},[a("img",{staticClass:"bg",attrs:{src:o("oosY"),alt:""}}),e._v(" "),a("div",{staticClass:"banner-content"},[e._m(0),e._v(" "),a("div",{staticClass:"banner-right"},[a("div",{staticClass:"video-container"},[e._m(1),e._v(" "),a("div",{staticClass:"video-content"},[a("div",{staticClass:"video-player",on:{click:e.playVideo}},[a("img",{staticClass:"video-cover",attrs:{src:o("VXhy"),alt:"视频封面"}}),e._v(" "),e._m(2)])])])])]),e._v(" "),a("VideoPlayer",{attrs:{visible:e.showVideoModal,"video-url":e.videoUrl,title:"一分钟之内带你了解易管通"},on:{close:e.closeVideoModal,loadstart:e.onVideoLoadStart,canplay:e.onVideoCanPlay,error:e.onVideoError}})],1)},staticRenderFns:[function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"banner-left"},[a("img",{staticClass:"ygt-icon",attrs:{src:o("1oX4"),alt:""}}),e._v(" "),a("h3",{staticClass:"b-title"},[e._v("企业管理“全域全场景”的智慧协同办公平台")]),e._v(" "),a("p",{staticClass:"sub-item"},[a("img",{attrs:{src:o("s10C"),alt:""}}),e._v(" "),a("span",[e._v("集AI老板、人资管理、行政管理、法务管理、财务管理为一体")])]),e._v(" "),a("p",{staticClass:"sub-item"},[a("img",{attrs:{src:o("s10C"),alt:""}}),e._v(" "),a("span",[e._v("利用AI人工智能、物联网、大数据、移动互联等先进技术，实现企业云端信息化管理")])]),e._v(" "),a("p",{staticClass:"sub-item"},[a("img",{attrs:{src:o("s10C"),alt:""}}),e._v(" "),a("span",[e._v("有效规范流程、降低运营成本，提高企业管理效率")])])])},function(){var e=this.$createElement,t=this._self._c||e;return t("div",{staticClass:"video-header"},[t("span",{staticClass:"dot"}),this._v(" "),t("span",{staticClass:"dot"}),this._v(" "),t("span",{staticClass:"dot"})])},function(){var e=this.$createElement,t=this._self._c||e;return t("div",{staticClass:"play-button"},[t("img",{staticClass:"play-icon",attrs:{src:o("XrZq"),alt:"播放"}})])}]};var r=o("VU/8")(i,s,!1,function(e){o("gwuD")},"data-v-5d002394",null).exports,d=o("Dd8w"),l=o.n(d),c=o("NYxO"),u={name:"ManagementModules",mixins:[o("JYzS").a],computed:l()({},Object(c.e)(["dashboardMenus"]),{secondNav:function(){return this.dashboardMenus.find(function(e){return"/dashboard/ygt"==e.path})||[]}}),data:function(){return{modules:[{id:1,name:"AI老板",tag:"让老板更省心，让员工更轻松",description:"自动拆解工作内容，输出标准化、可执行的员工工作清单",icon:o("Jgde"),gradient:"linear-gradient(117deg, #D6E8FF, #EBFBFE)",borderColor:"#D6E8FF",tagBg:"#CEE5FF",lineColor:"#D7E9FF",buttonColor:"linear-gradient(90deg, #167BFF, #2F8CFD)",route:"/dashboard/ygt/ai-boss"},{id:2,name:"行政管理",tag:"数智化行政办公平台",description:"深入业务全场景，提升企业的办公效率",icon:o("fJKm"),gradient:"linear-gradient(117deg, #CCFFF3, #ECFFFA)",borderColor:"#C2F9EC",tagBg:"#BCF8EA",lineColor:"#C2F9EC",buttonColor:"linear-gradient(90deg, #03BF72, #07D381)",route:"/dashboard/ygt/administration"},{id:3,name:"人资管理",tag:"一站式人力资源管理平台",description:"赋能老板决策，全面掌控人才“选、育、用、留”",icon:o("R4FX"),gradient:"linear-gradient(117deg, #D7D9FF, #F3F1FE)",borderColor:"#DEE0FF",tagBg:"#D3D5FF",lineColor:"#E7E7FF",buttonColor:"linear-gradient(90deg, #5453E2, #6362F3)",route:"/dashboard/ygt/hr"},{id:4,name:"法务管理",tag:"新一代Ai赋能",description:"合同全生命周期管理系统",icon:o("MtVH"),gradient:"linear-gradient(117deg, #D6E8FF, #EBFBFE)",borderColor:"#D6E8FF",tagBg:"#CEE5FF",lineColor:"#D7E9FF",buttonColor:"linear-gradient(90deg, #167BFF, #2F8CFD)",route:"/dashboard/ygt/legal"},{id:5,name:"财务管理",tag:"实时移动查看经营报表",description:"让财务工作化繁为简，老板全面掌控财务状态",icon:o("m9ol"),gradient:"linear-gradient(117deg, #F0DDFE, #F3ECFF)",borderColor:"#F0DDFE",tagBg:"#EBD2FF",lineColor:"#E7E7FF",buttonColor:"linear-gradient(90deg, #8553E2, #7D62F3)",route:"/dashboard/ygt/finance"}]}},methods:{handleModuleClick:function(e){var t=this.secondNav.children.find(function(t){return t.path==e.route});t?this.menuToPage(t):(console.log("点击了模块:",e.name),this.$router.push(e.route),this.$emit("module-click",e))}}},v={render:function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("div",{staticClass:"management-modules"},[o("div",{staticClass:"module-grid"},e._l(e.modules,function(t){return o("div",{key:t.id,staticClass:"module-card",style:{background:t.gradient,borderColor:t.borderColor},on:{click:function(o){return e.handleModuleClick(t)}}},[o("div",{staticClass:"model-content"},[o("div",{staticClass:"module-icon"},[o("img",{attrs:{src:t.icon,alt:t.name}})]),e._v(" "),o("h3",{staticClass:"module-title"},[e._v(e._s(t.name))]),e._v(" "),o("div",{staticClass:"module-tag",style:{background:t.tagBg}},[e._v(e._s(t.tag))]),e._v(" "),o("p",{staticClass:"module-description"},[e._v(e._s(t.description))])]),e._v(" "),o("div",{staticClass:"btn-wrap",style:{borderColor:t.lineColor}},[o("div",{staticClass:"module-button",style:{background:t.buttonColor}},[o("span",[e._v("点击进入")]),e._v(" "),o("i",{staticClass:"el-icon-arrow-right"})])])])}),0)])},staticRenderFns:[]};var g={name:"dashboardYgt",components:{HeaderBanner:r,ManagementModules:o("VU/8")(u,v,!1,function(e){o("a6jL")},"data-v-7c57aad9",null).exports},methods:{handleModuleClick:function(e){console.log("父组件接收到模块点击事件:",e.name)}}},h={render:function(){var e=this.$createElement,t=this._self._c||e;return t("div",{staticClass:"ygt-wrapper"},[t("HeaderBanner"),this._v(" "),t("ManagementModules",{on:{"module-click":this.handleModuleClick}})],1)},staticRenderFns:[]};var m=o("VU/8")(g,h,!1,function(e){o("P+wk")},"data-v-c1b2bca2",null);t.default=m.exports},a6jL:function(e,t){},gwuD:function(e,t){}});