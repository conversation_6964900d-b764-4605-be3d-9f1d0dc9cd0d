webpackJsonp([84],{bTHw:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=a("Dd8w"),n=a.n(r),i=a("pI5c"),l=a("cMGX"),o=a("Wxq9"),s=a("2Eno"),c=(a("lbHh"),a("mMqg")),d=a("NYxO"),p={name:"BranchList",components:{Pagination:l.a},data:function(){return{searchName:"",total:1,branchList:[],addBranchVisible:!1,branchForm:{id:"",company_name:"",parant_id:"",logo:"",located:"",phone:"",address:"",industry:[],scale:"",website:""},defaultForm:{id:"",company_name:"",parant_id:"",logo:"",located:"",phone:"",address:"",industry:[],scale:"",website:""},rules:{company_name:[{required:!0,message:"请输入企业名称",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}],located:[{required:!0,message:"请输入所在城市",trigger:"blur"}],parant_id:[{required:!0,message:"请选择父级公司",trigger:"change"}],phone:[{required:!0,message:"请输入手机号",trigger:"blur"}],address:[{required:!0,message:"请输入详细地址",trigger:"blur"}],industry:[{required:!0,message:"请输入行业类型",trigger:"blur"}],scale:[{required:!0,message:"请选择公司规模",trigger:"blur"}],website:[{required:!1,message:"请输入企业网址",trigger:"blur"}]},options:{ORGANIZATION_CITY:[],ORGANIZATION_SCALE:[]},provinceAndCityData:o.regionData,upload_url:"/api/upload",logo:"",dialogTitle:"",parentCompany:[],loading:!1,queryData:{unique_id:null,page:1,limit:10,selectField:""},disableEdit:!1}},computed:n()({},Object(d.e)(["userInfo"]),{token:function(){return this.$cookies.get("token")||this.$cookies.get("access_token")||this.$store.getters.token}}),filters:{getIndustry:function(e){return e?"[object String]"===Object.prototype.toString.call(e)?e.split(",").map(function(e){return parseInt(e)}):e:""}},watch:{userInfo:{handler:function(e){e?(this.queryData.unique_id=e.company.unique_id,this.getBranchList()):(this.getUserInfo(),this.getBranchList())},deep:!0,immediate:!0}},methods:n()({getBranchList:function(){var e=this;this.loading=!0,Object(c.b)(this.queryData).then(function(t){e.loading=!1,200===t.code&&(e.branchList=t.data.data,e.total=t.data.total,e.branchList.map(function(e){return e.city=e.located&&e.located.length?e.located.split(","):""}))})}},Object(d.d)(["setUserInfo"]),{getUserInfo:function(){var e=this;Object(i.X)({access_token:this.token,type:"pc"}).then(function(t){200===t.code&&e.setUserInfo(t.data)})},handleEditBranch:function(e){this.dialogTitle="编辑",this.addBranchVisible=!0,this.branchForm=e,this.branchForm.parent_id=this.$set(this.branchForm,"parant_id",e.parent_id),this.branchForm.phone=this.$set(this.branchForm,"phone",e.mobile),this.branchForm.company_name=this.$set(this.branchForm,"company_name",e.enterprise_name),this.branchForm.located=this.$set(this.branchForm,"located",e.city),this.logo=e.picurl,this.branchForm.scale=this.$set(this.branchForm,"scale",e.company_size),this.branchForm.industry=this.$set(this.branchForm,"industry","[object String]"===Object.prototype.toString.call(e.industry_id)?e.industry_id.split(",").map(function(e){return parseInt(e)}):e.industry_id),this.disableEdit=!0},handleDelBranch:function(e){var t=this;this.$confirm("确认要删除该分公司？").then(function(e){t.$message.success("删除成功")}).catch(function(e){})},getOptions:function(){var e=this;this.options.ORGANIZATION_CITY=this.provinceAndCityData,this.options.ORGANIZATION_SCALE=Object(s.a)(),Object(i.s)().then(function(t){e.options.ORGANIZATION_TRADE=e.getTreeData(t.data)})},getTreeData:function(e){for(var t=0;t<e.length;t++)e[t].child.length<1?e[t].child=void 0:this.getTreeData(e[t].child);return e},companyCreate:function(){var e=this;this.$refs.companyInfoForm.validate(function(t){if(!t)return console.log("error submit!!"),!1;var a={parant_id:"[object String]"===Object.prototype.toString.call(e.branchForm.parant_id)||"[object Number]"===Object.prototype.toString.call(e.branchForm.parant_id)?e.branchForm.parant_id:e.branchForm.parant_id.join(","),phone:e.branchForm.phone,company_name:e.branchForm.company_name,located:e.branchForm.located.join(","),address:e.branchForm.address,industry:e.branchForm.industry.join(","),scale:e.branchForm.scale,website:e.branchForm.website,logo:e.branchForm.logo};e.branchForm.id&&(a.id=e.branchForm.id),Object(c.d)(a).then(function(t){200===t.code&&("[object Object]"===Object.prototype.toString.call(t.data)?(e.$message.success(e.dialogTitle+"成功"),e.cancelCreate(),e.getBranchList()):e.$message.error(e.dialogTitle+"失败"))})})},cancelCreate:function(){this.branchForm=this.defaultForm,this.addBranchVisible=!1},handleSuccess:function(e,t){e.data.length&&(this.branchForm.logo=e.data[0],this.logo=URL.createObjectURL(t.raw))},getParentCompany:function(){var e=this,t={unique_id:this.userInfo.company.unique_id};Object(c.c)(t).then(function(t){200===t.code&&(e.parentCompany=e.clearChildren(t.data))})},searchByName:function(){this.getBranchList()},clearChildren:function(e){for(var t=0;t<e.length;t++)e[t].children.length<1?e[t].children=void 0:this.clearChildren(e[t].children);return e}}),created:function(){this.getOptions(),this.getParentCompany()}},h={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-card",[a("div",{staticClass:"clearfix center-flex",attrs:{slot:"header"},slot:"header"},[a("span",[e._v("分公司列表")]),e._v(" "),a("div",{staticClass:"center-flex"},[a("div",{staticClass:"center-flex"},[a("el-input",{attrs:{size:"small",clearable:"",placeholder:"请输入公司名称或手机号"},model:{value:e.queryData.selectField,callback:function(t){e.$set(e.queryData,"selectField",t)},expression:"queryData.selectField"}},[a("i",{staticClass:"el-input__icon el-icon-search",attrs:{slot:"prefix"},slot:"prefix"})]),e._v(" "),a("el-button",{attrs:{size:"small",type:"primary"},on:{click:e.searchByName}},[e._v("搜索")])],1),e._v(" "),a("el-button",{attrs:{size:"small"},on:{click:function(t){e.addBranchVisible=!0,e.dialogTitle="添加",e.disableEdit=!1}}},[e._v("添加分公司")])],1)]),e._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.branchList,width:"100%"}},[a("el-table-column",{attrs:{type:"index",width:"80px",align:"center",label:"序号"}}),e._v(" "),a("el-table-column",{attrs:{prop:"picurl",width:"100px",align:"center",label:"logo"},scopedSlots:e._u([{key:"default",fn:function(e){return[a("el-image",{staticStyle:{width:"60px",height:"60px"},attrs:{src:e.row.picurl,"preview-src-list":[e.row.picurl]}})]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"enterprise_name",align:"center",label:"企业名称"}}),e._v(" "),a("el-table-column",{attrs:{prop:"mobile",align:"center",label:"手机号"}}),e._v(" "),a("el-table-column",{attrs:{prop:"city",align:"center",label:"所在城市"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-cascader",{staticClass:"table-cascader",attrs:{options:e.options.ORGANIZATION_CITY,filterable:"",clearable:"",disabled:"",placeholder:""},model:{value:t.row.city,callback:function(a){e.$set(t.row,"city",a)},expression:"scope.row.city"}})]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"address",align:"center",label:"详细地址"}}),e._v(" "),a("el-table-column",{attrs:{prop:"hangye",align:"center",label:"行业类型"}}),e._v(" "),a("el-table-column",{attrs:{prop:"statusInfo",align:"center",label:"审批状态"}}),e._v(" "),a("el-table-column",{attrs:{fixed:"right",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.handleEditBranch(t.row)}}},[e._v("编辑")])]}}])})],1),e._v(" "),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],staticStyle:{"text-align":"center"},attrs:{total:e.total,page:e.queryData.page,limit:e.queryData.limit},on:{"update:page":function(t){return e.$set(e.queryData,"page",t)},"update:limit":function(t){return e.$set(e.queryData,"limit",t)},pagination:e.getBranchList}})],1),e._v(" "),a("el-dialog",{attrs:{visible:e.addBranchVisible,title:e.dialogTitle+"分公司","custom-class":"branch-dialog","before-close":e.cancelCreate,width:"600px"},on:{"update:visible":function(t){e.addBranchVisible=t}}},[a("el-form",{ref:"companyInfoForm",staticClass:"demo-form-inline",attrs:{rules:e.rules,model:e.branchForm,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"父级企业",prop:"parant_id"}},[a("el-cascader",{attrs:{options:e.parentCompany,placeholder:"请选择企业",span:12,props:{checkStrictly:!0},"show-all-levels":!1,clearable:""},model:{value:e.branchForm.parant_id,callback:function(t){e.$set(e.branchForm,"parant_id",t)},expression:"branchForm.parant_id"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"手机号",prop:"phone"}},[a("el-input",{attrs:{placeholder:"请输入手机号",span:12,clearable:"",disabled:e.disableEdit},model:{value:e.branchForm.phone,callback:function(t){e.$set(e.branchForm,"phone",t)},expression:"branchForm.phone"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"企业名称",prop:"company_name"}},[a("el-input",{attrs:{placeholder:"请输入企业名称",span:12,clearable:""},model:{value:e.branchForm.company_name,callback:function(t){e.$set(e.branchForm,"company_name",t)},expression:"branchForm.company_name"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"所在城市",prop:"located"}},[a("el-cascader",{attrs:{options:e.options.ORGANIZATION_CITY,filterable:"",clearable:"",placeholder:"可搜索"},model:{value:e.branchForm.located,callback:function(t){e.$set(e.branchForm,"located",t)},expression:"branchForm.located"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"详细地址",prop:"address"}},[a("el-input",{attrs:{placeholder:"请输入详细地址",clearable:""},model:{value:e.branchForm.address,callback:function(t){e.$set(e.branchForm,"address",t)},expression:"branchForm.address"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"企业LOGO",prop:"logo"}},[a("el-upload",{staticClass:"avatar-uploader",attrs:{name:"files[]",data:{token:e.token},action:e.upload_url,"show-file-list":!1,"on-success":e.handleSuccess}},[e.logo?a("el-image",{staticClass:"avatar",attrs:{fit:"contain",src:e.logo}}):a("i",{staticClass:"el-icon-plus avatar-uploader-icon"})],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"行业类型",prop:"industry"}},[a("el-cascader",{attrs:{options:e.options.ORGANIZATION_TRADE,props:{label:"title",children:"child",value:"id"},clearable:""},model:{value:e.branchForm.industry,callback:function(t){e.$set(e.branchForm,"industry",t)},expression:"branchForm.industry"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"公司规模",prop:"scale"}},[a("el-select",{attrs:{placeholder:"请选择公司规模"},model:{value:e.branchForm.scale,callback:function(t){e.$set(e.branchForm,"scale",t)},expression:"branchForm.scale"}},e._l(e.options.ORGANIZATION_SCALE,function(e,t){return a("el-option",{key:t,attrs:{label:e,value:e}})}),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"企业网址",prop:"website"}},[a("el-input",{attrs:{placeholder:"请输入企业网址",clearable:""},model:{value:e.branchForm.website,callback:function(t){e.$set(e.branchForm,"website",t)},expression:"branchForm.website"}})],1),e._v(" "),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:e.companyCreate}},[e._v("保存")]),e._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:e.cancelCreate}},[e._v("取消")])],1)],1)],1)],1)},staticRenderFns:[]};var u=a("VU/8")(p,h,!1,function(e){a("xfAB")},"data-v-53e8c0fa",null);t.default=u.exports},xfAB:function(e,t){}});