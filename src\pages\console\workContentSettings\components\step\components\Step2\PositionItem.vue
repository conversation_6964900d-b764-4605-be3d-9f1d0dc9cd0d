<script>
import '@/assets/style/common.scss'
import WorkTags from '../WorkTags.vue'
import {addWorkContent, delWorkContent, editWorkContent, updatePositionApi, recreatWorkContentApi} from '../../../../../../../api/newTask'

export default {
  name: 'PositionItem',
  components: {WorkTags},
  props: {
    positionList: {
      type: Array,
      default: () => ([])
    }
  },
  data () {
    return {
      expandedCard: {
        row: null,
        column: null
      },
      isDragging: false,
      dragStartItem: null,
      dragStartIndex: null,
      dragStartCategoryIndex: null,
      mouseDownTime: 0,
      dragOverIndex: null,
      dragOverCategoryIndex: null,
      dragTargetItem: null,
      dragTargetIndex: null,
      dragTargetCategoryIndex: null,
      showEditDialog: false,
      editIndex: undefined,
      editForm: {
        id: '',
        positionName: '',
        workContents: []
      },
      editLoading: false,
      draggedElClone: null,
      offsetX: 0,
      offsetY: 0,
      aiCreateLoading: false
    }
  },
  methods: {
    handleEdit (item, index) {
      this.editIndex = index
      this.editForm.id = item.id
      this.editForm.positionName = item.name
      this.editForm.workContents = item.list
      this.showEditDialog = true
    },
    async saveEdit () {
      try {
        this.editLoading = true
        const res = await updatePositionApi({
          name: this.editForm.positionName,
          id: this.editForm.id,
          type: 1
        })
        if (res.code === 200) {
          this.$message.success('修改成功')
          this.$emit('refresh', false)
        } else {
          this.$message.error(res.message)
        }
      } catch (error) {
        this.$message.error(error.message)
      } finally {
        // 更新岗位信息逻辑
        this.editLoading = false
        this.showEditDialog = false
      }
    },
    cancelEdit () {
      this.showEditDialog = false
    },
    toggleExpand (column, row) {
      if (this.expandedCard.column === column && this.expandedCard.row === row) {
        this.expandedCard = {
          row: null,
          column: null
        }
      } else {
        this.expandedCard = {column, row}
      }
    },
    handleDelete (positionItem, positionIndex, index) {
      this.$confirm('确认删除该岗位吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 删除前先收起展开的卡片
        if (this.expandedCard.column === positionIndex && this.expandedCard.row === index) {
          this.expandedCard = {
            row: null,
            column: null
          }
        }
        // 触发删除事件
        this.$emit('delete', {
          positionItem,
          positionIndex,
          categoryIndex: index
        })
      }).catch(() => {
      })
    },
    handleMouseDown (e, positionItem, positionIndex, index) {
      if (this.expandedCard.column === positionIndex && this.expandedCard.row === index) {
        return
      }

      if (event.target.classList.contains('delete-icon') ||
          event.target.closest('.delete-icon')) {
        return // 完全跳过删除按钮区域的拖拽处理
      }
      this.mouseDownTime = Date.now()
      this.dragStartItem = positionItem
      this.dragStartIndex = positionIndex
      this.dragStartCategoryIndex = index

      const originalEl = e.target.closest('.position-item')
      if (!originalEl) return

      this.isDragging = true
      originalEl.classList.add('dragging')

      // Create clone for dragging
      const rect = originalEl.getBoundingClientRect()
      const clone = originalEl.cloneNode(true)

      this.offsetX = e.clientX - rect.left
      this.offsetY = e.clientY - rect.top

      clone.style.position = 'fixed'
      clone.style.left = `${rect.left}px`
      clone.style.top = `${rect.top}px`
      clone.style.width = `${rect.width}px`
      clone.style.height = `${rect.height}px`
      clone.style.zIndex = '9999'
      clone.style.pointerEvents = 'none'
      clone.style.opacity = '0.85'
      clone.style.transition = 'none'
      clone.classList.remove('is-drag-over', 'is-expanded')
      clone.style.background = '#fff'
      clone.style.boxShadow = '0 8px 32px rgba(77,128,255,0.15), 0 2px 12px rgba(0,0,0,0.12)'
      clone.style.borderRadius = '4px'
      clone.style.padding = '17px 14px'
      clone.style.overflow = 'hidden'

      // 给虚拟卡片内的岗位图片加右边距
      const img = clone.querySelector('.position-name-left img')
      if (img) {
        img.style.marginRight = '9px'
      }

      this.draggedElClone = clone
      document.body.appendChild(this.draggedElClone)

      document.addEventListener('mousemove', this.handleMouseMove)
      document.addEventListener('mouseup', this.handleMouseUp)
      console.log('Mouse down:', { positionItem, positionIndex, categoryIndex: index })
    },
    handleMouseMove (e) {
      if (!this.isDragging || !this.draggedElClone) return
      requestAnimationFrame(() => {
        if (this.draggedElClone) {
          this.draggedElClone.style.left = `${e.clientX - this.offsetX}px`
          this.draggedElClone.style.top = `${e.clientY - this.offsetY}px`
        }
      })
    },
    handleMouseUp (e) {
      document.removeEventListener('mousemove', this.handleMouseMove)
      document.removeEventListener('mouseup', this.handleMouseUp)

      if (this.draggedElClone) {
        this.draggedElClone.remove()
        this.draggedElClone = null
      }

      // 先清理 dragging 样式
      document.querySelectorAll('.dragging').forEach(el => el.classList.remove('dragging'))

      // 立即重置 isDragging，确保样式恢复
      this.isDragging = false

      if (!this.dragStartItem) {
        return
      }

      if (
        !this.dragTargetItem ||
        !this.dragTargetItem.id ||
        !this.dragStartItem ||
        !this.dragStartItem.id ||
        this.dragTargetItem.id === this.dragStartItem.id
      ) {
        // 不触发合并
        return
      }
      // 检查是否按住鼠标超过200ms
      if (Date.now() - this.mouseDownTime >= 200) {
        console.log('Mouse up - trigger merge:', {
          source: {
            positionItem: this.dragStartItem,
            positionIndex: this.dragStartIndex,
            categoryIndex: this.dragStartCategoryIndex
          },
          target: {
            positionItem: this.dragTargetItem,
            positionIndex: this.dragTargetIndex,
            categoryIndex: this.dragTargetCategoryIndex
          }
        })

        // 触发合并事件
        this.$emit('drop', {
          source: {
            positionItem: this.dragStartItem,
            positionIndex: this.dragStartIndex,
            categoryIndex: this.dragStartCategoryIndex
          },
          target: {
            positionItem: this.dragTargetItem,
            positionIndex: this.dragTargetIndex,
            categoryIndex: this.dragTargetCategoryIndex
          }
        })
      }

      // 清理状态
      this.dragStartItem = null
      this.dragStartIndex = null
      this.dragStartCategoryIndex = null
      this.dragTargetItem = null
      this.dragTargetIndex = null
      this.dragTargetCategoryIndex = null
      this.dragOverIndex = null
      this.dragOverCategoryIndex = null
    },
    handleMouseOver (e, targetItem, targetIndex, index) {
      if (!this.dragStartItem ||
          (this.dragStartIndex === targetIndex && this.dragStartCategoryIndex === index)) {
        // 拖拽回原卡片时，清空 dragTargetItem
        this.dragTargetItem = null
        this.dragTargetIndex = null
        this.dragTargetCategoryIndex = null
        this.dragOverIndex = null
        this.dragOverCategoryIndex = null
        return
      }

      // 只更新拖动经过的位置和视觉状态
      this.dragOverIndex = targetIndex
      this.dragOverCategoryIndex = index
      this.dragTargetItem = targetItem
      this.dragTargetIndex = targetIndex
      this.dragTargetCategoryIndex = index
    },
    handleMouseLeave () {
      if (this.isDragging) {
        this.dragOverIndex = null
        this.dragOverCategoryIndex = null
      }
    },
    async handleRemoveContents (item) {
      if (!item || !item.id) return
      const res = await delWorkContent({ id: item.id })
      if (res.code === 200) {
        this.$message.success('删除成功')
      } else {
        this.$message.error(res.msg || res.message || '删除失败')
      }
    },
    async handleAddContents (content, position) {
      if (!content || !position || !position.id) return
      try {
        const res = await addWorkContent({
          name: content,
          gw_id: position.id
        })
        if (res.code === 200) {
          this.$message.success('添加成功')
          // this.editForm.workContents[this.editForm.workContents.length - 1].id = res.data.id
          position.list[0].id = res.data.id
          this.$emit('refresh', false)
        } else {
          this.$message.error(res.msg || res.message || '添加失败')
          //   删掉新增的
          position.list.splice(0, 1)
        }
      } catch (error) {
        //   删掉新增的
        position.list.splice(0, 1)
        this.$message.error('添加失败')
      }
    },
    async handleEditContents (item, position) {
      if (!item || !position || !position.id) return
      try {
        const res = await editWorkContent({
          id: item.id,
          name: item.name,
          gw_id: position.id
        })
        if (res.code === 200) {
          this.$message.success('修改成功')
        } else {
          this.$message.error(res.msg || res.message || '修改失败')
        }
      } catch (error) {
        this.$message.error('修改失败')
      }
    },
    async handleAICreate (positionItem) {
      if (!positionItem.id) return
      try {
        this.aiCreateLoading = true
        const res = await recreatWorkContentApi({
          id: positionItem.id
        })
        if (res.code === 200) {
          this.$message.success('创建成功')
          this.$emit('refresh', false)
        } else {
          this.$message.error(res.msg || res.message || '创建失败')
        }
      } catch (e) {
        console.log(e)
        this.$message.error('创建失败')
      } finally {
        this.aiCreateLoading = false
      }
    }
  }
}
</script>

<template>
  <div>
    <div v-if="positionList.length">
      <div v-for="(position, index) in positionList" :key="index" class="position-item-wrap">
        <template v-if="position.list && position.list.length">
          <div class="flex flex-align-center">
            <div class="position-cate">{{position.name}}</div>
            <div class="tips flex flex-align-center">
              <img :src="require('@/assets/image/console/work/waring.png')" class="waring" alt="">
              鼠标移入岗位后按住
              <img :src="require('@/assets/image/console/work/drag.png')" class="drag" alt="">
              进行拖拽，拖拽至另一岗位上可实现岗位合并
            </div>
          </div>
          <div class="position-content">
            <el-row :gutter="16">
              <el-col :lg="6" :md="8" :xs="12" v-for="(positionItem, positionIndex) in position.list" :key="positionIndex">
                <div
                    class="position-item"
                    :class="{
                      'is-expanded': expandedCard.column === positionIndex && expandedCard.row === index,
                      'is-dragging': isDragging && dragStartIndex === positionIndex && dragStartCategoryIndex === index,
                      'is-drag-over': dragOverIndex === positionIndex && dragOverCategoryIndex === index,
                      'can-drop': isDragging && dragStartIndex !== positionIndex && dragStartCategoryIndex !== index
                    }"
                    @mouseover.stop.prevent="handleMouseOver($event, positionItem, positionIndex, index)"
                    @mouseleave.stop.prevent="handleMouseLeave"
                >
                  <div
                      class="position-name-wrap flex flex-align-center flex-justify-between"
                  >
                    <div class="position-name-left flex flex-align-center">
                      <img :src="require('@/assets/image/console/work/job.png')" alt="">
                      <span class="position-name">{{positionItem.name}}</span>
                    </div>
                    <div class="position-action flex-align-center">
                      <img :src="require('@/assets/image/console/work/edit.png')" alt="" @click.stop.prevent="handleEdit(positionItem, positionIndex, index)">
                      <img
                          :src="require('@/assets/image/console/work/delete.png')"
                          alt=""
                          @click.stop.prevent="handleDelete(positionItem, positionIndex, index)"
                          class="delete-icon"
                      >
                      <img v-if="expandedCard.column !== positionIndex || expandedCard.row !== index"
                          :src="require('@/assets/image/console/work/drag.png')"
                          alt=""
                          class="drag-icon"
                          @mousedown.stop.prevent="handleMouseDown($event, positionItem, positionIndex, index)"
                      >
                    </div>
                  </div>
                  <div class="position-work-contents" :class="{ 'is-expanded': expandedCard.column === positionIndex && expandedCard.row === index }">
                    <WorkTags v-if="positionItem.aick" :list="positionItem.list" :show-add="true" :show-close="true" :showAutoCreateBtn="true" @add="content => handleAddContents(content, positionItem)" @remove="handleRemoveContents" @edit="data => handleEditContents(data, positionItem)" @aiCreate="handleAICreate(positionItem)" :ai-create-loading="aiCreateLoading" />
                    <el-empty v-else :image="require('@/assets/image/console/empty.png')" :image-size="100" style="padding: 0" description="正在生成中..." />
                  </div>
                  <div class="more flex flex-align-center flex-justify-center" v-if="positionItem.aick ? positionItem.list ? positionItem.list.length > 2 : false : false">
                    <el-button type="text" @click="toggleExpand(positionIndex, index)">
                      <span>{{ expandedCard.column === positionIndex && expandedCard.row === index ? '收起' : `查看全部${positionItem.list.length}项工作内容` }}</span>
                      <img
                          :src="require('@/assets/image/console/work/arrow2.png')"
                          alt=""
                          :class="{ 'is-expanded': expandedCard.column === positionIndex && expandedCard.row === index }"
                      >
                    </el-button>
                  </div>
                </div>
              </el-col>
            </el-row>
          </div>
        </template>
      </div>
      <el-dialog title="编辑岗位" :visible.sync="showEditDialog" :close-on-click-modal="false" width="400px">
        <el-form :model="editForm" label-position="top">
          <el-form-item label="岗位名称">
            <el-input v-model="editForm.positionName" placeholder="请输入岗位名称"></el-input>
          </el-form-item>
          <!--<el-form-item label="工作内容">
            <WorkTags :list="editForm.workContents" :show-add="true" :show-close="true" @add="handleAddContents" @remove="handleRemoveContents" @edit="handleEditContents"/>
          </el-form-item>-->
        </el-form>
        <div slot="footer" class="dialog-footer flex flex-justify-center flex-align-center">
          <el-button @click="cancelEdit">取 消</el-button>
          <el-button type="primary" @click="saveEdit" :loading="editLoading">保存修改</el-button>
        </div>
      </el-dialog>
    </div>
    <el-empty v-else :image="require('@/assets/image/console/empty.png')" :image-size="300" description="正在生成中..." />
  </div>
</template>

<style scoped lang="scss">
.position-item-wrap{
  --el-primary-color: #4D80FF;
  & + .position-item-wrap{
    margin-top: calc(41px - 16px);
  }
  .position-cate{
    font-weight: bold;
    font-size: 18px;
    color: #000000;
  }
  .tips{
    margin-left: 15px;
    font-size: 14px;
    color: #999999;
    .waring{
      margin-right: 7px;
    }
  }
  .position-content{
    margin-top: 20px;
    /deep/ .el-row{
      margin-left: 0!important;
      margin-right: 0!important;
      .el-col{
        position: relative;
        height: calc(190px + 16px);
      }
    }
    .position-item{
      margin-bottom: 16px;
      background: #F2F6F9;
      border-radius: 4px;
      height: 190px;
      padding: 17px 14px;
      position: relative;
      left: 8px;
      top: 0;
      right: 8px;
      box-sizing: border-box;
      overflow: hidden;
      .position-name-left{
        width: 70%;
      }
      .position-name{
        font-size: 16px;
        color: #000000;
        margin-left: 9px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        display: inline-block;
      }
      .position-action{
        img + img{
          margin-left: 6px;
        }
      }
      /deep/ .ai-agent-tag{
        min-width: auto;
        min-height: 36px;
        height: auto;
        padding-top: 6px;
        padding-bottom: 6px;
        span{
          white-space: wrap;
          line-height: 1.5;
        }
      }
      .position-work-contents{
        margin-top: 16px;
        height: 90px;
        overflow: hidden;
      }
      .more{
        background: #F2F6F9;
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 30px;
        padding: 5px 0;
        /deep/ .el-button{
          font-size: 14px;
          color: #999999;
        }
      }
      transition: all 0.3s ease;
      z-index: 1;
      user-select: none;

      &.is-dragging {
        opacity: 0.5;
      }

      &.is-expanded {
        z-index: 10;
        position: absolute;
        left: 0;
        right: 0;
        background: #F2F6F9;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        border-radius: 4px;
        height: auto !important;
        min-height: 230px;
        cursor: default;
      }

      &.is-drag-over {
        cursor: move !important;
        transform: scale(1.02);
        box-shadow: 0 0 0 2px #4D80FF;
        background: rgba(77, 128, 255, 0.1);
        z-index: 5;

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(77, 128, 255, 0.1);
          border-radius: 4px;
          z-index: -1;
        }
      }

      &.can-drop {
        cursor: move !important;
        &::after {
          content: '';
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 40px;
          height: 40px;
          background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%234D80FF"><path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/></svg>') no-repeat center center;
          opacity: 0.5;
          pointer-events: none;
        }
      }

      &:hover {
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
      }
    }

    .position-work-contents {
      transition: height 0.3s ease;
      height: 90px;
      overflow: hidden;

      /deep/.el-empty__description{
        margin-top: 5px;
      }

      &.is-expanded {
        height: auto;
        min-height: 90px;
        padding-bottom: 20px;
      }
    }

    .more {
      img {
        transition: transform 0.3s ease;
        margin-left: 4px;

        &.is-expanded {
          transform: rotate(180deg);
        }
      }
    }
  }
}

.position-action {
  .delete-icon {
    cursor: pointer;
    transition: opacity 0.3s;

    &:hover {
      opacity: 0.8;
    }
  }
}

.position-name-wrap {
  cursor: pointer;
  user-select: none;

  &.dragging {
    opacity: 0.5;
  }
}

@keyframes drag-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(77, 128, 255, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(77, 128, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(77, 128, 255, 0);
  }
}

:deep(.position-item-wrap) {
  * {
    cursor: inherit;
  }
}

/deep/ .el-dialog__title{
  font-weight: bold;
  font-size: 18px;
  color: #111111;
}

/deep/ .el-dialog__body {
  padding: 20px;
}

/deep/ .el-form-item__label{
  font-size: 16px;
  color: #555555;
  font-weight: normal;
}

.dialog-footer{
  display: flex;
  justify-content: center;
  align-items: center;
  /deep/ .el-button {
    width: 100px;
    height: 38px;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0;
    border-radius: 4px;
    font-size: 16px;
    &.el-button--primary {
      border: var(--el-color-primary);
      background: var(--el-color-primary);
    }
  }
}
/deep/ .el-empty__description{
  margin-top: 5px;
}
</style>
