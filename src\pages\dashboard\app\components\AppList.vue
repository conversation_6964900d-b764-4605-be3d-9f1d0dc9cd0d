<template>
  <div class="app-list-wrapper">
    <app-item v-if="showAdd" type="add" @click="handleAddApp" />
    <AppItem v-for="(item, index) in list" :key="index" :item="item" :showEdit="showAdd" @edit="handleEdit" @delete="handleDelete" />
    <AddApp ref="addAppRef" @success="handleSuccess" />
  </div>
</template>

<script>
import AppItem from './AppItem.vue'
import AddApp from './AddApp.vue'
import { getThirdAppListApi, delThirdAppApi } from '@/api/consoleNew'

export default {
  name: "AppList",
  components: {
    AppItem,
    AddApp
  },
  data() {
    return {
      showAdd: false,
      list: [
        // {
        //   icon: require('@/assets/image/dashboard/app/lc.png'),
        //   title: '龙采erp',
        //   desc: '智能一体化企业管理系统，助力降本增效、数字化升级！',
        //   link: 'https://erp1.ailongcai.com:12973/pc/finance/index/'
        // },
        // {
        //   icon: require('@/assets/image/dashboard/app/qd.png'),
        //   title: '抢单平台',
        //   desc: '极速抢单平台，智能匹配供需，让赚钱快人一步！',
        //   link: 'https://flexible.china9.cn/enterprise/'
        // },
        // {
        //   icon: require('@/assets/image/dashboard/app/dls.png'),
        //   title: '代理商平台',
        //   desc: '全链路赋能代理，智能撮合商机，共赢增长新生态！',
        //   link: 'https://w2.china9.cn/'
        // },
      ]
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    async getList() {
      const res = await getThirdAppListApi()
      console.log(res, '三方应用列表')
      if (res.code === 200) {
        const { list, power } = res.data
        this.list = list
        this.showAdd = power
      }
    },
    handleAddApp() {
      // 打开添加应用弹窗
      this.$refs.addAppRef.openDialog()
    },
    handleSuccess() {
      // 保存成功后刷新列表
      this.getList()
    },
    handleEdit(item) {
      // 处理编辑应用
      console.log('编辑应用:', item)
      // 这里可以打开编辑弹窗，传入当前应用数据
      this.$refs.addAppRef.openDialog(item)
    },
    handleDelete(item) {
      // 处理删除应用
      console.log('删除应用:', item)
      this.$confirm('确定要删除这个应用吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 这里调用删除API
        delThirdAppApi({ id: item.id }).then(res => {
          if (res.code === 200) {
            this.$message.success('删除成功')
            this.getList()
          }
        })
      }).catch(() => {
        // this.$message.info('已取消删除')
      })
    }
  }
};
</script>

<style scoped lang="scss">
.app-list-wrapper {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 16px;
  flex-wrap: wrap;
}
@media screen and (max-width: 1500px) {
  .app-list-wrapper {
    grid-template-columns: repeat(5, 1fr);
  }
}
@media screen and (max-width: 1300px) {
  .app-list-wrapper {
    grid-template-columns: repeat(4, 1fr);
  }
}
@media screen and (max-width: 1100px) {
  .app-list-wrapper {
    grid-template-columns: repeat(3, 1fr);
  }
}
@media screen and (max-width: 900px) {
  .app-list-wrapper {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>