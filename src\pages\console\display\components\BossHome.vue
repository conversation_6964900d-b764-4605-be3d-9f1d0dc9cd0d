<template>
  <div class="console-main">
    <el-row :gutter="20">
      <el-col :lg="18" :md="24" :sm="24" :xs="24" class="left">
        <div class="flex" style="width: 100%; align-items: stretch; gap: 16px;">
          <div class="flex" style="flex-direction: column; overflow: visible;width: calc(100% - 380px - 16px);">
            <div
              class="latest-notice flex-align-center"
              v-loading="noticeLoading"
            >
              <img :src="bell" alt="" />
              <span class="latest-notice-title">最新通知</span>
              <ul class="flex-1 flex-align-center flex-space-between">
                <li
                  v-for="item in latestNotice"
                  :key="item.id"
                  :id="item.id"
                  class="flex-1"
                >
                  <router-link
                    :to="`/console/display/newsDetail/${item.id}`"
                    class="flex-align-center"
                  >
                    <h4 class="text-ellipsis">{{ item.title }}</h4>
                    <time>{{ item.publish_at }}</time>
                  </router-link>
                </li>
              </ul>
              <div class="more">
                <router-link
                  to="/company/information/list?type=detail&cate=2"
                  class="flex-align-center"
                >
                  <span style="color: #8F8F8F;">更多</span>
                  <img :src="arrow" alt="" />
                </router-link>
              </div>
            </div>
            <card-wrap class="news-wrap mt-16 flex-1">
              <el-table
                :data="news"
                header-row-class-name="header-row"
                row-class-name="body-row"
                style="height: 100%"
                v-loadmore="load"
                v-loading="newsLoading"
                @row-click="toNewsDetail"
              >
                <el-table-column label="新闻分类" prop="type" width="120">
                  <template slot-scope="scope">
                    <span class="text-ellipsis type"
                      >【{{ scope.row.type_name }}】</span
                    >
                  </template>
                </el-table-column>
                <el-table-column label="标题内容" prop="title" show-overflow-tooltip />
                <el-table-column
                  label="发布时间"
                  prop="publish_at"
                  width="200"
                />
              </el-table>
            </card-wrap>
          </div>
          <ai-chat style="width: 380px" @toMore="toAITools" :input-height="130" />
        </div>
        <el-row class="mt-16 mb-16" v-loading="taskLoading">
          <el-col :span="24">
            <card-wrap class="task-wrap">
              <task />
            </card-wrap>
          </el-col>
        </el-row>
      </el-col>
      <el-col :lg="6" :md="24" :sm="24" :xs="24" class="right box">
        <el-row class="ai-tools-box">
          <el-col :span="24">
            <user-info />
          </el-col>
        </el-row>
        <el-row class="mt-16">
          <el-col :span="24">
            <card-wrap title="常用操作" class="common-operation-wrap">
              <common-operation
                v-for="(item, index) in commonActions"
                :key="index"
                :title="item.title"
                :list="item.list"
              />
            </card-wrap>
          </el-col>
        </el-row>
        <el-row class="mt-16">
          <el-col :span="24">
            <card-wrap title="今日工作计划">
              <template slot="right">
                <el-button class="default" @click="addPlan">
                  <img :src="reportIcon" alt="" />
                  <span>新增计划</span>
                </el-button>
              </template>
              <schedule class="schedule" :schedule="schedule" />
            </card-wrap>
          </el-col>
        </el-row>
        <el-row class="mt-16">
          <card-wrap
            class="attendance-statistics"
            :title="`考勤统计（${nowDate.month}）`"
          >
            <card-list-new
              :card-list="clockingIn"
              :col-setting="{ lg: 6, md: 4, sm: 4, xs: 8 }"
              :item-style="{ padding: 0, marginBottom: '38px', cursor: 'default' }"
            >
              <template slot="content" slot-scope="{ item }">
                <template v-if="item.value">
                  <div class="value" v-html="item.value"></div>
                </template>
                <div v-else class="value">0</div>
                <div class="label">{{ item.label }}</div>
              </template>
            </card-list-new>
          </card-wrap>
        </el-row>
      </el-col>
    </el-row>

    <!-- 创建日程 -->
    <create-schedule
      ref="createScheduleRef"
      @updateTodoList="getScheduleData"
    />
  </div>
</template>

<script>
import '@/assets/style/common.scss'
import bell from '@/assets/image/console/my/bossNew/bell.png'
import arrow from '@/assets/image/console/my/bossNew/arrow.png'
import progressBarBg from '@/assets/image/console/my/bossNew/progress-bar.png'
import reportIcon from '@/assets/image/console/my/bossNew/report.png'
import CardWrap from './CardWrap.vue'
import ZhGoods from '../mixins/zhGoods'
import CommonOperation from './home/<USER>'
import { mapActions, mapGetters, mapState } from 'vuex'
import { getAttendance, getSchedule } from '../../../../api/consoleNew'
import CreateSchedule from '../employee/components/CreateSchedule.vue'
import Schedule from './Schedule.vue'
import CardListNew from './CardListNew.vue'
import AiToolsCard from './home/<USER>'
import { getType, padStart } from '../../../../utils'
import { getMyTaskApi } from '../../../../api/newTask'
import { companyNews } from '@/api/console'
import Task from './home/<USER>/Task.vue'
import AiChat from './AiChat.vue'
import UserInfo from './UserInfo.vue'
import Cookies from 'js-cookie'

export default {
  name: 'BossHome',
  props: {
    role: {
      type: String,
      default: ''
    }
  },
  components: {
    AiToolsCard,
    CardListNew,
    Schedule,
    CreateSchedule,
    CommonOperation,
    CardWrap,
    Task,
    AiChat,
    UserInfo
  },
  mixins: [ZhGoods],
  data () {
    return {
      bell,
      arrow,
      progressBarBg,
      reportIcon,
      // 最新通知
      latestNotice: [],
      // 新闻列表
      news: [],
      page: 1,
      lastPage: 1,
      //  今日任务完成进度
      progress: 20,
      //  任务列表
      taskList: {
        0: {
          cateName: '未执行',
          list: []
        },
        1: {
          cateName: '进行中',
          list: []
        },
        2: {
          cateName: '已完成',
          list: []
        }
      },
      schedule: [],
      clockingIn: [
        {
          label: '正常',
          value: 0
        },
        {
          label: '迟到',
          value: 0
        },
        {
          label: '早退',
          value: 0
        },
        {
          label: '缺卡',
          value: 0
        },
        {
          label: '旷工',
          value: 0
        },
        {
          label: '请假',
          value: 0
        },
        {
          label: '出差',
          value: 0
        },
        {
          label: '外出',
          value: 0
        },
        {
          label: '调休',
          value: 0
        },
        {
          label: '加班',
          value: 0
        },
        {
          label: '值班',
          value: 0
        }
      ],
      taskLoading: false,
      newsLoading: false,
      noticeLoading: false,
      showTask: false
    }
  },
  computed: {
    ...mapState(['zhUserAuth', 'buyGoodsList']),
    ...mapGetters(['taskTypeMap']),
    /**
     * 进度条背景图
     */
    progressBarBgImg () {
      return `url(${this.progressBarBg})`
    },
    /**
     * 常用操作
     * @returns {[{title: string, list: [{path: string, logo: *, title: string, pathType: string},{ref: string, logo: *, title: string},{ref: string, logo: *, title: string},{ref: string, logo: *, title: string},{ref: string, logo: *, title: string},null]},{title: string, list: [{path: string, logo: *, title: string, pathType: string},{path: string, roles: string[], logo: *, title: string, pathType: string},{path: string, roles: string[], logo: *, title: string, pathType: string},{path: string, roles: string[], show: boolean, logo: *, goods: string, title: string, pathType: string},{path: string, logo: *, title: string, pathType: string},null,null]},{title: string, list: [{path: string, logo: *, title: string, pathType: string},{path: string, logo: *, title: string, pathType: string},{path: string, logo: *, title: string, pathType: string}]}]}
     */
    commonActions () {
      let ympAuth = false
      if (this.buyGoodsList && this.buyGoodsList.includes('ymp')) {
        ympAuth = true
      }
      let ympRoles = ['yuncard']
      if (this.zhUserAuth) {
        ympAuth = ympRoles.some(role => this.zhUserAuth.includes(role))
      }

      return [
        {
          title: 'OA审批',
          list: [
            {
              logo: require(`@/assets/image/console/console/spzx.png`),
              title: '审批中心',
              pathType: 'page',
              path: '/console/display/console/webview/approval'
            },
            {
              logo: require('@/assets/image/console/my/employee/icon2.png'),
              title: '外出',
              ref: 'goOutRef'
            },
            {
              logo: require('@/assets/image/console/my/employee/icon3.png'),
              title: '请假',
              ref: 'leaveFormRef'
            },
            {
              logo: require('@/assets/image/console/my/employee/icon4.png'),
              title: '加班',
              ref: 'overtimeRef'
            },
            {
              logo: require('@/assets/image/console/my/employee/icon5.png'),
              title: '出差',
              ref: 'businessTripRef'
            },
            {
              logo: require('@/assets/image/console/my/employee/icon6.png'),
              title: '调休',
              ref: 'compensatoryLeaveRef'
            }
          ]
        },
        {
          title: '电子办公',
          list: [
            {
              logo: require(`@/assets/image/console/console/xrb.png`),
              title: '写日报',
              pathType: 'page',
              path: '/console/display/console/webview/report'
            },
            {
              logo: require(`@/assets/image/console/console/tjkh.png`),
              title: '添加客户',
              pathType: 'page',
              path: '/console/display/console/webview/customer',
              roles: []
            },
            {
              logo: require(`@/assets/image/console/console/srlr.png`),
              title: '录入订单',
              pathType: 'page',
              path: '/console/display/console/webview/revenueEntry',
              roles: []
            },
            {
              logo: require(`@/assets/image/console/console/ymp.png`),
              title: '名片管理',
              pathType: 'page',
              path: '/cards',
              goods: 'ymp',
              roles: ['yuncard'],
              show: ympAuth
            },
            /* {
              logo: require(`@/assets/image/console/console/khb.png`),
              title: '看汇报',
              pathType: 'page',
              path: '/console/display/console/webview/report?tab=2'
            }, */
            {
              logo: require(`@/assets/image/console/console/rwdt.png`),
              title: '任务大厅',
              pathType: 'page',
              path: '/console/display/console/webview/hall'
            },
            {
              logo: require(`@/assets/image/console/console/wdrw.png`),
              title: '我的任务',
              pathType: 'page',
              path: '/console/display/console/webview/myTask'
            }
          ]
        },
        {
          title: '我的信息',
          list: [
            {
              logo: require(`@/assets/image/console/console/wdda.png`),
              title: '我的档案',
              pathType: 'page',
              path: '/console/display/console/employee/archives'
            },
            {
              logo: require(`@/assets/image/console/human/approval.png`),
              title: '我的考勤',
              pathType: 'page',
              path: '/console/display/console/webview/attendance'
            },
            {
              logo: require(`@/assets/image/console/console/wdgz.png`),
              title: '我的工资',
              pathType: 'page',
              path: '/console/display/console/webview/salary'
            }
          ]
        }
      ]
    },
    /**
     * 获取当前年月日，用汉字年月日拼接，月和日需要2位数字，用0补齐
     * @returns {{total: string, month: string}}
     */
    nowDate () {
      const date = new Date()
      const year = date.getFullYear()
      const month = date.getMonth() + 1
      const day = date.getDate()
      return {
        total: `${year}年${padStart('' + month, 2, '0')}月${padStart(
          '' + day,
          2,
          '0'
        )}日`,
        month: `${year}年${padStart('' + month, 2, '0')}月`
      }
    },
  },
  // 监听表格滚动
  directives: {
    loadmore: {
      bind (el, binding) {
        const selectWrap = el.querySelector('.el-table__body-wrapper')
        selectWrap.addEventListener('scroll', function () {
          const scrollDistance =
            this.scrollHeight - this.scrollTop - this.clientHeight
          if (scrollDistance <= 50) {
            binding.value() // 执行在使用时绑定的函数，在这里即loadMorePerson方法
          }
        })
      }
    }
  },
  methods: {
    /**
     * 新增计划
     */
    addPlan () {
      this.$refs.createScheduleRef.openDialog()
    },
    /**
     * 获取日程
     */
    async getScheduleData () {
      const res = await getSchedule({ company_id: this.company_id })
      const { code, data } = res
      if (code === 200 && data) {
        const { list } = data
        this.schedule = list.map(v => {
          //  把v.created_at和v.endTime中2024-07-06 09:44:59的时间转为只需要时分秒的格式
          return {
            ...v,
            time: v.created_at.slice(11, 16) + '-' + v.endTime.slice(11, 16)
          }
        })
      }
    },
    /**
     * boss获取考勤统计数据
     */
    async getClockingInData () {
      const date = this.nowDate.total
        .replace('年', '-')
        .replace('月', '-')
        .replace('日', '')
      const res = await getAttendance({ date })
      const { monthkq } = res
      if (getType(monthkq) !== 'Object') return
      this.clockingIn[0].value = monthkq.normal
      this.clockingIn[1].value = monthkq.late
      this.clockingIn[2].value = monthkq.early
      this.clockingIn[3].value = monthkq.lack
      this.clockingIn[4].value = monthkq.absenteeism
      this.clockingIn[5].value = monthkq.leave
      this.clockingIn[6].value = monthkq.b_travel
      this.clockingIn[7].value = monthkq.goout
      this.clockingIn[8].value = monthkq.leave_lieu
      this.clockingIn[9].value = monthkq.overtime
      this.clockingIn[10].value = monthkq.duty

      this.clockingIn.forEach(v => {
        //  把v.value中的汉字用span标签包裹
        v.value = ('' + v.value).replace(
          /[\u4e00-\u9fa5]/g,
          '<span class="small">$&</span>'
        )
      })
    },
    /**
     * 跳转ai工具
     */
    toAITools () {
      this.$emit('toAiTools')
    },
    /**
     * 获取任务执行状态文字
     */
    getStatusText (status) {
      const taskStatusMap = {
        0: '未开始',
        1: '进行中',
        2: '已完成'
      }
      return taskStatusMap[status]
    },
    /**
     * 获取我的任务列表
     */
    async getMyTask () {
      this.taskLoading = true
      try {
        const res = await getMyTaskApi()
        const { code, data } = res
        if (code === 200 && data) {
          this.showTask = data.length
          for (let taskListKey in this.taskList) {
            const taskListValue = this.taskList[taskListKey]
            this.$set(
              taskListValue,
              'list',
              JSON.parse(
                JSON.stringify(
                  data
                    .filter(v => +v.status === +taskListKey)
                    .map(v => {
                      const isCustom = v.qltype === 3
                      const original = JSON.parse(JSON.stringify(v))
                      return {
                        ...v,
                        statusText: this.getStatusText(v.status),
                        title: v.name,
                        type: isCustom ? 0 : v.perform_type,
                        desc: isCustom
                          ? v.questlog[0]
                            ? v.questlog[0].name
                            : ''
                          : '',
                        start: v.begin_time,
                        end: v.end_time,
                        duration: v.hnum ? v.hnum + 'h' : v.inum + 'min',
                        original: original
                      }
                    })
                )
              )
            )
          }
        }
      } catch (error) {
        console.log('[ error ] >', error)
      } finally {
        this.taskLoading = false
      }
    },
    /**
     * 改变我的任务状态
     * @param task
     */
    changeMyTaskStatus (task) {
      if (!task) return
      const oldStatus = task.status
      if (task.isrepeat && oldStatus === 2) {
        task.status = 1
      } else {
        task.status++
      }
      this.taskList[oldStatus].list = this.taskList[oldStatus].list.filter(
        v => v.id !== task.id
      )
      task.statusText = this.getStatusText(task.status)
      this.taskList[task.status].list.push(task)
    },
    /**
     * 获取新闻列表
     */
    async getNewsList () {
      if (this.page > this.lastPage) {
        this.page = this.lastPage
        return
      }
      this.page === 1 && (this.newsLoading = true)
      try {
        // wdtype 是否过滤掉新闻
        const res = await companyNews({ page: this.page, limit: 3, wdtype: 1 })
        const { code, data } = res
        if (code === 200 && data) {
          if (this.page === 1) {
            this.news = data.data
          } else {
            this.news = this.news.concat(data.data)
          }
          this.lastPage = data.last_page
        }
      } catch (error) {
        console.log('[ error ] >', error)
      } finally {
        this.page === 1 && (this.newsLoading = false)
      }
    },
    /**
     * 表格触底加载
     */
    load () {
      this.page++
      this.getNewsList()
      console.log('load')
    },
    /**
     * 获取最新通知
     */
    async getLatestNotice () {
      try {
        this.noticeLoading = true
        const res = await companyNews({ page: this.page, type: 2 })
        const { code, data } = res
        if (code === 200 && data) {
          // this.latestNotice 的值为 data.data的前2条数据
          this.latestNotice = data.data.slice(0, 2)
        }
      } catch (error) {
        console.log('[ error ] >', error)
      } finally {
        this.noticeLoading = false
      }
    },
    /**
     * 跳转新闻详情
     */
    toNewsDetail (row, column, event) {
      if (row.id) {
        this.$router.push('/console/display/newsDetail/' + row.id)
      }
    }
  },
  mounted () {
    this.getScheduleData()
    this.getClockingInData()
    this.getMyTask()
    this.getNewsList()
    this.getLatestNotice()
  }
}
</script>

<style lang="scss" scoped>
$mainColor: #4d80ff;
/deep/ .self-main-btn{
  height: 40px;
  background: $mainColor;
  border-radius: 4px;
  border: 1px solid $mainColor;
  padding: 0 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 15px;
}
.news-wrap {
  display: flex;
  flex-direction: column;
  /deep/ .content {
    flex: 1;
  }
}
.console-main {
  padding: 13px;
  /deep/ .card{
    padding: 18px 17px;
  }
  /deep/ .tools{
    margin-top: 11px!important;
    margin-bottom: -11px!important;
    .list {
      margin-bottom: 11px;
      align-items: stretch;
      .list-item {
        padding: 11px;
        height: 51px;
      }
    }
  }
  /deep/ .el-button.default {
    height: 40px;
    background: #ffffff;
    border-radius: 4px;
    border: 1px solid #4d80ff;
    font-size: 15px;
    color: #4d80ff;
    box-sizing: border-box;
    padding: 0 8px;
    span {
      display: flex;
      align-items: center;
      justify-content: center;
    }
    img {
      margin-right: 8px;
    }
  }
  .mt-16 {
    margin-top: 16px;
  }
  .mb-16 {
    margin-bottom: 20px;
  }
  .latest-notice {
    width: 100%;
    padding: 12px 25px;
    background: rgba(234, 240, 255, 0.5);
    border-radius: 10px;
    border: 1px solid rgba(77, 128, 255, 0.5);
    box-sizing: border-box;
    .latest-notice-title {
      font-size: 16px;
      color: #4d80ff;
      margin-left: 9px;
    }
    ul {
      margin-left: 35px;
      li {
        h4 {
          max-width: 60%;
          padding-right: 43px;
          font-size: 16px;
          color: #222222;
          font-weight: normal;
        }
        time {
          font-size: 16px;
          color: #666666;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
    .more {
      font-size: 14px;
      color: #8f8f8f;
      img {
        margin-left: 5px;
      }
    }
  }
  .news-wrap {
    /deep/ .header-row {
      th.el-table__cell {
        background: #f2f6f9;
        font-size: 14px;
        color: #3c3c3c;
        height: 40px;
        padding: 8px 0;
      }
    }
    /deep/ .body-row {
      td.el-table__cell {
        padding: 10px 0;
        font-size: 14px;
        color: #3c3c3c;
        cursor: pointer;
      }
    }
    .type {
      font-size: 14px;
      color: #5588f1;
    }
  }
  .right.box{
    /deep/ .card {
      padding-top: 12px;
      padding-bottom: 12px;
    }
  }
  .common-operation-wrap {
    .group {
      margin-top: 11px;
      padding: 13px 13px 3px 13px;
      /deep/ .card-item {
        margin-bottom: 13px !important;
        .info-box {
          .title {
            margin-top: 5px !important;
          }
        }
      }
      /deep/ .list-wrap{
        margin-top: 14px;
      }
    }
  }
  .schedule {
    margin-top: 23px;
  }
  .attendance-statistics {
    /deep/ .card-list {
      margin-top: 13px;
      margin-bottom: -13px;
      .card-item {
        margin-bottom: 13px!important;
        .info-box {
          display: flex;
          flex-direction: column;
          align-items: center;
          .title {
            display: none;
          }
          .value {
            font-weight: 800;
            font-size: 20px;
            color: #111111;
            letter-spacing: 2px;
            span {
              font-size: 12px;
              color: #111111;
              font-weight: normal;
            }
          }
          .label {
            font-size: 14px;
            color: #3c3c3c;
            margin-top: 2px;
          }
        }
      }
    }
  }
}
@media screen and (min-width: 1880px) {
  .ai-wrap .ai-list {
    /deep/ .list {
      margin-bottom: 0;
    }
  }
}
@media screen and (max-width: 1880px) and (min-width: 1500px) {
  .console-main {
    .task-wrap
      .task-type
      .task-type-list
      .list
      /deep/
      .el-col:not(.file-list__item) {
      width: 50%;
    }
    .attendance-statistics {
      /deep/ .card-list {
        .el-col-lg-6 {
          width: 30%;
        }
      }
    }
  }
}
@media screen and (max-width: 1800px) and (min-width: 1500px) {
  .console-main {
    .task-wrap .task-type .task-type-list .list /deep/ .el-col {
      width: 50%;
    }
    .attendance-statistics {
      /deep/ .card-list {
        .el-col-lg-6 {
          width: 50%;
        }
      }
    }
    /deep/ .group .list-wrap {
      .el-col-lg-8 {
        width: 50%;
      }
    }
  }
}
@media screen and (max-width: 1500px) and (min-width: 1200px) {
  .console-main {
    & > /deep/ .el-row {
      & > .el-col {
        width: 100%;
      }
    }
    $col: 7;
    .attendance-statistics {
      /deep/ .card-list {
        .el-col-lg-6 {
          width: calc(100% / #{$col});
        }
      }
    }
    /deep/ .group .list-wrap {
      .el-col-lg-8 {
        width: calc(100% / #{$col});
      }
    }
  }
}
@media screen and (max-width: 1300px) and (min-width: 1200px) {
  .console-main {
    & > .el-row {
      & > .left,
      & > .right {
        width: 100%;
      }
    }
  }
}
@media screen and (max-width: 1200px) {
  .console-main {
    /deep/ .group .list-wrap {
      margin-top: 40px;
    }
  }
}
</style>
