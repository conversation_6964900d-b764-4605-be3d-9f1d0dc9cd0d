<template>
  <el-dialog :title="title" :visible.sync="visible" width="606px">
    <el-form :model="form" :rules="rules" ref="form" label-width="70px" label-position="left">
      <el-form-item label="LOGO" prop="icon">
        <LogoUpload v-model="form.icon" @success="handleLogoSuccess" />
      </el-form-item>
      <el-form-item label="名称" prop="title">
        <el-input v-model="form.title" placeholder="请输入应用名称" clearable />
      </el-form-item>
      <el-form-item label="网址" prop="url">
        <el-input v-model="form.url" placeholder="请输入应用网址" clearable />
      </el-form-item>
      <el-form-item label="描述" prop="remarks">
        <el-input type="textarea" v-model="form.remarks" placeholder="请输入应用描述" :rows="4" :maxlength="40" show-word-limit resize="none" clearable />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取 消</el-button>
      <el-button type="primary" @click="handleSubmit">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import LogoUpload from '@/components/LogoUpload.vue'
import { saveThirdAppApi } from '@/api/consoleNew'

export default {
  name: "AddApp",
  components: {
    LogoUpload
  },
  data() {
    return {
      visible: false,
      form: {
        title: '',
        icon: '',
        remarks: '',
        url: ''
      },
      rules: {
        title: [{required: true, message: '请输入应用名称', trigger: 'blur'}],
        icon: [{required: true, message: '请上传应用图标', trigger: 'blur'}],
        remarks: [{required: true, message: '请输入应用描述', trigger: 'blur'}],
        url: [
          { required: true, message: '请输入应用链接', trigger: 'blur' },
          { validator: (rule, value, callback) => {
              if (!/^https?:\/\/.*/.test(value)) {
                callback(new Error('请输入正确的链接'))
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ]
      }
    };
  },
  computed: {
    title() {
      return this.form.id ? '编辑应用' : '添加应用'
    }
  },
  methods: {
    openDialog(row = null) {
      this.visible = true
      if (row) {
        const { id, title, icon, remarks, url } = row
        this.form = { id, title, icon, remarks, url }
      } else {
        this.form = { title: '', icon: '', remarks: '', url: '' }
      }
    },
    handleLogoSuccess(fileData) {
      // LOGO上传成功的回调
      console.log('LOGO上传成功-----:', fileData)
      this.form.icon = fileData
    },
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          // 表单验证通过，处理提交逻辑
          console.log('表单提交:', this.form)
          // 这里可以添加具体的提交逻辑
          // this.$emit('submit', this.form)
          let params = JSON.parse(JSON.stringify(this.form))
          saveThirdAppApi(params).then(res => {
            if (res.code === 200) {
              let tips = this.form.id ? '编辑成功' : '添加成功'
              this.$message.success(tips)
              this.$emit('success')
              this.visible = false
            } else {
              // this.$message.error(res.message)
            }
          })
        } else {
          console.log('表单验证失败')
          return false
        }
      })
    }
  }
};
</script>