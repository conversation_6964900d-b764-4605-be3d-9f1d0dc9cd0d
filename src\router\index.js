import Vue from 'vue'
import Router from 'vue-router'
import Login from '@/pages/home/<USER>'
import IndexHome from '@/pages/index/home'
import IndexConsole from '@/pages/index/console'
import DashboardConsole from '@/pages/dashboard/index'
import IndexConsoleLeftSide from '@/pages/index/console-left-side'
import OpenPlatformIndex from '@/pages/openPlatform/OpenPlatformIndex'
import LandingIndex from '../pages/landingPage/LandingIndex.vue'
import Cookies from 'vue-cookies'
import store from '../store'
import {Message, MessageBox} from 'element-ui'
import BuyDialogPopup from '../components/buy/index'

// 解决ElementUI导航栏中的vue-router在3.0版本以上重复点菜单报错问题
const originalPush = Router.prototype.push
Router.prototype.push = function push (location) {
  return originalPush.call(this, location).catch(err => err)
}
Vue.use(Router)

const router = new Router({
  routes: [
    {
      path: '/login',
      name: 'login',
      meta: {
        statistic: '登录'
      },
      component: Login
    },
    {
      path: '/home',
      name: 'HomeNew',
      component: () => import('@/pages/home/<USER>'),
      meta: {
        requireAuth: false,
        index: 0,
        statistic: '首页'
      }
    },
    {
      path: '/legal/index',
      name: 'legalIFrame',
      component: () => import('@/pages/console/display/legal/index'),
      meta: {
        requireAuth: true,
        statistic: '法务管理',
        showFooter: false,
        goods: 'egt',
        showNav: false
      }
    },
    {
      path: '/finance/index',
      name: 'financeIFrame',
      component: () => import('@/pages/console/display/finance/index'),
      meta: {
        requireAuth: true,
        statistic: '财务管理',
        showFooter: false,
        goods: 'egt',
        showNav: false
      }
    },
    {
      path: '/administration/index',
      name: 'administrationIFrame',
      component: () => import('@/pages/console/display/administration/index'),
      meta: {
        requireAuth: true,
        statistic: '行政管理',
        showFooter: false,
        goods: 'egt',
        showNav: false
      }
    },
    {
      path: '/',
      name: 'IndexHome',
      component: IndexHome,
      redirect: '/home',
      meta: {
        requireAuth: false
      },
      children: [
        {
          path: '/home/<USER>',
          name: 'About',
          component: () => import('@/pages/home/<USER>'),
          meta: {
            requireAuth: false,
            statistic: '关于我们'
          }
        },
        {
          path: '/home/<USER>',
          name: 'Cooperation',
          component: () => import('@/pages/home/<USER>'),
          meta: {
            requireAuth: false,
            index: 4,
            statistic: '合作伙伴与生态'
          }
        },
        {
          path: '/home/<USER>',
          name: 'CloudProduct',
          component: () => import('@/pages/home/<USER>'),
          meta: {
            requireAuth: false,
            index: 1,
            statistic: '云产品'
          }
        },
        {
          path: '/home/<USER>',
          name: 'Solution',
          component: () => import('@/pages/home/<USER>/solution'),
          meta: {
            requireAuth: false,
            index: 2,
            statistic: '解决方案'
          }
        },
        {
          path: '/home/<USER>/list',
          name: 'SolutionList',
          component: () => import('@/pages/home/<USER>/list'),
          meta: {
            requireAuth: false,
            index: 2,
            statistic: '通用解决方案列表'
          }
        },
        {
          path: '/home/<USER>/detail',
          name: 'SolutionDetail',
          component: () => import('@/pages/home/<USER>/detail'),
          meta: {
            requireAuth: false,
            index: 2,
            statistic: '通用解决方案详情'
          }
        },
        {
          path: '/home/<USER>/list',
          name: 'IndustryList',
          component: () => import('@/pages/home/<USER>/list'),
          meta: {
            requireAuth: false,
            index: 2,
            statistic: '行业解决方案列表'
          }
        },
        {
          path: '/home/<USER>/detail',
          name: 'IndustryDetail',
          component: () => import('@/pages/home/<USER>/detail'),
          meta: {
            requireAuth: false,
            index: 2,
            statistic: '行业解决方案详情'
          }
        },
        {
          path: '/home/<USER>',
          name: 'SupportAndService',
          component: () => import('@/pages/home/<USER>'),
          meta: {
            requireAuth: false,
            index: 3,
            statistic: '支持与服务'
          }
        },
        {
          path: '/home/<USER>',
          name: 'Document',
          component: () => import('@/pages/home/<USER>'),
          meta: {
            requireAuth: false,
            statistic: '帮助文档',
            keepAlive: true
          }
        },
        // {
        //   path: '/home/<USER>',
        //   name: 'NewActivity',
        //   component: () => import('@/pages/home/<USER>'),
        //   meta: {
        //     requireAuth: false,
        //     index: 5,
        //     statistic: '最新活动'
        //   }
        // },
        {
          path: '/home/<USER>/detail',
          name: 'NewActivityDetail',
          component: () => import('@/pages/home/<USER>/detail'),
          meta: {
            requireAuth: false,
            index: 2,
            statistic: '活动详情'
          }
        },
        {
          path: '/home/<USER>/detail',
          name: 'questionHomeDetail',
          component: () => import('@/pages/home/<USER>/questionHomeDetail'),
          meta: {
            requireAuth: false,
            statistic: '常见问题详情'
          }
        }
      ]
    },
    {
      path: '/console',
      name: 'IndexConsole',
      component: DashboardConsole,
      redirect: '/console',
      meta: {
        requireAuth: true
      },
      children: [
        {
          path: '/console',
          name: 'Console',
          component: () => import('@/pages/console/indexOld'),
          meta: {
            requireAuth: true,
            statistic: '控制台'
          }
        },
        {
          path: '/consoleNew',
          name: 'ConsoleNew',
          component: () => import('@/pages/console/index'),
          meta: {
            requireAuth: true,
            statistic: '控制台'
          }
        },
        {
          path: '/shopcar',
          name: 'Shopcar',
          component: () => import('@/pages/console/shopcar'),
          meta: {
            requireAuth: true,
            statistic: '购物车'
          }
        },
        {
          path: '/sureOrder',
          name: 'SureOrder',
          component: () => import('@/pages/console/sureOrder'),
          meta: {
            requireAuth: true,
            statistic: '确认订单'
          }
        },
        {
          path: '/pay',
          name: 'Pay',
          component: () => import('@/pages/console/pay'),
          meta: {
            requireAuth: true,
            statistic: '支付'
          }
        },
        {
          path: '/buy',
          name: 'buy',
          component: () => import('@/pages/console/Buy'),
          meta: {
            requireAuth: true,
            index: 0,
            statistic: '产品购买',
            title: '产品购买',
            showFooter: false
          }
        },
        {
          path: '/buySuccess',
          name: 'buySuccess',
          component: () => import('@/pages/console/BuySuccess'),
          meta: {
            requireAuth: true,
            index: 0,
            statistic: '购买成功',
            title: '购买成功',
            showFooter: false
          }
        },
        {
          path: '/company/create',
          name: 'CompanyCreate',
          component: () => import('@/pages/console/company/create'),
          meta: {
            requireAuth: true,
            statistic: '创建企业'
          }
        },
        {
          path: '/news/index',
          name: 'News',
          component: () => import('@/pages/console/news/index'),
          meta: {
            requireAuth: false,
            statistic: '新闻列表'
          }
        },
        {
          path: '/news/detail',
          name: 'NewsDetail',
          component: () => import('@/pages/console/news/detail'),
          meta: {
            requireAuth: false,
            statistic: '新闻详情'
          }
        },
        {
          path: '/question/list',
          name: 'questionList',
          component: () => import('@/pages/home/<USER>/questionList'),
          meta: {
            requireAuth: false,
            statistic: '常见问题'
          }
        },
        {
          path: '/question/detail',
          name: 'questionDetail',
          component: () => import('@/pages/home/<USER>/questionDetail'),
          meta: {
            requireAuth: false,
            statistic: '常见问题详情'
          }
        },
        {
          path: '/console/website',
          name: 'website',
          component: () => import('@/pages/console/website'),
          meta: {
            requireAuth: true,
            statistic: '统一网站登录'
          }
        },
        {
          path: '/console/display',
          name: 'display',
          component: () => import('@/pages/dashboard/components/RouterView'),
          redirect: '/console/display/console',
          meta: {
            requireAuth: true,
            statistic: '控制台',
            showFooter: false
          },
          children: [
            {
              path: '/console/display/console',
              name: 'consoleWrap',
              redirect: '/console/display/console',
              component: () => import('@/pages/console/display/employee/index'),
              meta: {
                requireAuth: true,
                statistic: '我的',
                icon: '/image/console/menu/icon1.png',
                showFooter: false
              },
              children: [
                {
                  path: '/console/ai-tools-page',
                  name: 'consoleAIToolsPage',
                  component: () => import('@/pages/console/display/components/AiToolsPage.vue'),
                  meta: {
                    requireAuth: true,
                    statistic: 'AI工具',
                    icon: '/image/console/menu/icon1.png',
                    showFooter: false,
                    hasPadding: true
                  },
                },
                {
                  path: '/console/display/console',
                  name: 'console',
                  component: () => import('@/pages/console/display/console'),
                  meta: {
                    requireAuth: true,
                    statistic: '我的',
                    icon: '/image/console/menu/icon1.png',
                    showFooter: false
                  }
                },
                {
                  path: '/console/display/console/webview/:path',
                  name: 'employeeWebview',
                  component: () => import('@/pages/console/display/employee/webview'),
                  meta: {
                    requireAuth: true,
                    statistic: '申请',
                    showFooter: false,
                    hidden: true
                  }
                },
                {
                  path: '/console/display/console/employee/archives',
                  name: 'employeeArchivesIframe',
                  component: () => import('@/pages/console/display/employee/Archives'),
                  meta: {
                    requireAuth: true,
                    statistic: '我的档案',
                    showFooter: false
                  }
                },
                {
                  path: '/console/display/newsDetail/:id',
                  name: 'displayNewsDetail',
                  component: () => import('@/pages/console/display/NewsDetail.vue'),
                  meta: {
                    requireAuth: true,
                    statistic: '新闻详情',
                    showFooter: false
                  }
                }
              ]
            },
            {
              path: '/console/display/easyManage',
              name: 'easyManage',
              component: () => import('@/pages/console/display/components/index'),
              meta: {
                requireAuth: true,
                statistic: '易管通',
                icon: '/image/console/menu/icon3.png',
                showFooter: false,
                goods: 'egt'
              },
              redirect: '/console/display/human/index',
              children: [
                {
                  path: '/console/display/ygt/index',
                  name: 'ygtIndex',
                  component: () => import('@/pages/console/display/ygt/index'),
                  meta: {
                    requireAuth: true,
                    statistic: '易管通',
                    showFooter: false,
                    goods: 'egt'
                  }
                },
                {
                  path: '/console/display/human/index',
                  name: 'humanIndex',
                  component: () => import('@/pages/console/display/human/index'),
                  meta: {
                    requireAuth: true,
                    statistic: '人资管理',
                    showFooter: false,
                    goods: 'egt'
                  }
                },
                {
                  path: '/console/display/administration/index',
                  name: 'administrationIndex',
                  component: () => import('@/pages/console/display/administration/index'),
                  meta: {
                    requireAuth: true,
                    statistic: '行政管理',
                    showFooter: false,
                    goods: 'egt',
                    showNav: true
                  }
                },
                {
                  path: '/console/display/administration/policy',
                  name: 'policy',
                  component: () => import('@/pages/console/display/administration/policy'),
                  meta: {
                    requireAuth: false,
                    statistic: '行政管理-制度政策',
                    showFooter: false,
                    showNav: true
                  }
                },
                {
                  path: '/console/display/administration/index/webview',
                  name: 'administrationWebview',
                  component: () => import('@/pages/console/display/administration/webview.vue'),
                  meta: {
                    requireAuth: true,
                    statistic: '',
                    showFooter: false
                  }
                },
                {
                  path: '/console/display/finance/index',
                  name: 'financeIndex',
                  component: () => import('@/pages/console/display/finance/index'),
                  meta: {
                    requireAuth: true,
                    statistic: '财务管理',
                    showFooter: false,
                    goods: 'egt',
                    showNav: true
                  }
                },
                {
                  path: '/console/display/legal/index',
                  name: 'legalIndex',
                  component: () => import('@/pages/console/display/legal/index'),
                  meta: {
                    requireAuth: true,
                    statistic: '法务管理',
                    showFooter: false,
                    goods: 'egt',
                    showNav: true
                  }
                }
              ]
            },
            {
              path: '/console/display/jztManage',
              name: 'jztManage',
              component: () => import('@/pages/console/display/components/index'),
              meta: {
                requireAuth: true,
                statistic: '建站通',
                icon: '/image/console/menu/icon2.png',
                showFooter: false,
                goods: 'jzt'
              },
              redirect: '/console/display/website',
              children: [
                {
                  path: '/console/display/website',
                  name: 'websiteIndex',
                  redirect: '/console/display/website',
                  component: () => import('@/pages/console/display/website/index'),
                  meta: {
                    requireAuth: true,
                    statistic: '网站管理',
                    showFooter: false,
                    goods: 'jzt'
                  },
                  children: [
                    {
                      path: '/console/display/website',
                      name: 'websiteWebsite',
                      component: () => import('@/pages/console/display/website/website'),
                      meta: {
                        requireAuth: true,
                        statistic: '网站管理',
                        showFooter: false,
                        hidden: true,
                        goods: 'jzt'
                      }
                    },
                    {
                      path: '/console/display/website/webview/:path',
                      name: 'websiteWebview',
                      component: () => import('@/pages/console/display/website/webview'),
                      meta: {
                        requireAuth: true,
                        statistic: '网站管理',
                        showFooter: false,
                        hidden: true,
                        goods: 'jzt'
                      }
                    }
                  ]
                },
                {
                  path: '/console/display/customer/index',
                  name: 'customerIndex',
                  component: () => import('@/pages/console/display/customer/index'),
                  meta: {
                    requireAuth: true,
                    statistic: '销售通',
                    showFooter: false,
                    goods: 'egt'
                  }
                },
                {
                  path: '/console/display/E_commerce',
                  name: 'ECommerce',
                  component: () => import('@/pages/console/display/components/index'),
                  meta: {
                    requireAuth: true,
                    statistic: '全域电商',
                    showFooter: false,
                    goods: 'jztAI'
                  },
                  redirect: '/console/display/E_commerce/index', // 默认跳转
                  children: [
                    {
                      path: '/console/display/E_commerce/index',
                      name: 'ECommerceIndex',
                      component: () => import('@/pages/console/display/e_commerce/index'),
                      meta: {
                        requireAuth: true,
                        statistic: '全域电商',
                        showFooter: false,
                        goods: 'jztAI'
                      }
                    },
                    {
                      path: '/console/display/E_commerce/dashboard',
                      name: 'ECommerceDashboard',
                      component: () => import('@/pages/console/display/e_commerce/dashboard'),
                      meta: {
                        requireAuth: true,
                        statistic: '电商通',
                        showFooter: false,
                        goods: 'jztAI'
                      }
                    },
                    {
                      path: '/console/display/E_commerce/operational',
                      name: 'ECommerceOperational',
                      component: () => import('@/pages/console/display/e_commerce/operational'),
                      meta: {
                        requireAuth: true,
                        statistic: '运营数据',
                        showFooter: false,
                        goods: 'jztAI'
                      }
                    },
                  ]
                },
                {
                  path: '/console/display/publicize',
                  name: 'publicizeIndex',
                  redirect: '/console/display/publicize',
                  component: () => import('@/pages/console/display/website/index'),
                  meta: {
                    requireAuth: true,
                    statistic: '企业宣传',
                    showFooter: false,
                    goods: 'jztAI'
                  },
                  children: [
                    {
                      path: '/console/display/publicize',
                      name: 'publicizeWebsite',
                      component: () => import('@/pages/console/display/publicize/index'),
                      meta: {
                        requireAuth: true,
                        statistic: '企业宣传',
                        showFooter: false,
                        hidden: true,
                        goods: 'jztAI'
                      }
                    },
                    {
                      path: '/console/display/publicize/webview/:path',
                      name: 'publicizeWebview',
                      component: () => import('@/pages/console/display/website/webview'),
                      meta: {
                        requireAuth: true,
                        statistic: '企业宣传',
                        showFooter: false,
                        hidden: true,
                        goods: 'jztAI'
                      }
                    }
                  ]
                },
                {
                  path: '/console/display/media/index',
                  name: 'mediaIndex',
                  component: () => import('@/pages/console/display/media/index'),
                  meta: {
                    requireAuth: true,
                    statistic: '媒体投放',
                    showFooter: false
                  }
                }
              ]
            },
            {
              path: '/console/display/aigc',
              name: 'aigc',
              component: () => import('@/pages/console/display/publicize/aigc'),
              meta: {
                requireAuth: true,
                statistic: 'AIGC及素材管理',
                icon: '/image/console/menu/icon4.png',
                showFooter: false,
                goods: 'aigc'
              }
            },
            // 系统管理
            {
              path: '/console/display/system',
              name: 'system',
              component: () => import('@/pages/console/display/components/index'),
              meta: {
                requireAuth: true,
                statistic: '系统管理',
                showFooter: false
              },
              redirect: '/console/display/system/organization',
              children: [
                {
                  path: '/console/display/system/organization',
                  name: 'SystemOrganization',
                  component: () => import('@/pages/console/company/organization'),
                  meta: {
                    requireAuth: true,
                    statistic: '组织架构',
                    showFooter: false
                  }
                },
                {
                  path: '/console/display/system/position',
                  name: 'SystemPosition',
                  component: () => import('@/pages/console/company/position'),
                  meta: {
                    requireAuth: true,
                    statistic: '职务管理',
                    showFooter: false
                  }
                },
                {
                  path: '/console/display/system/role',
                  name: 'SystemRole',
                  component: () => import('@/pages/console/company/role'),
                  meta: {
                    requireAuth: true,
                    statistic: '角色管理',
                    showFooter: false
                  }
                },
                {
                  path: '/console/display/system/meeting',
                  name: 'SystemMeeting',
                  component: () => import('@/pages/console/company/meeting/index'),
                  meta: {
                    requireAuth: true,
                    statistic: '词汇库设置',
                    showFooter: false
                  }
                },
                // 私有化配置
                {
                  path: '/console/display/system/private',
                  name: 'SystemPrivate',
                  component: () => import('@/pages/console/company/private/index'),
                  meta: {
                    requireAuth: true,
                    statistic: '私有化配置',
                    showFooter: false
                  }
                }
              ]
            },
            {
              path: '/console/employee',
              name: 'employee',
              component: () => import('@/pages/console/display/employee/index'),
              meta: {
                requireAuth: true,
                statistic: '员工管理',
                showFooter: false,
                goods: 'egt'
              },
              children: [
                {
                  path: '/console/employee/archives',
                  name: 'employeeArchives',
                  component: () => import('@/pages/console/display/employee/Archives'),
                  meta: {
                    requireAuth: true,
                    statistic: '我的档案',
                    showFooter: false
                  }
                }
              ]
            },
            {
              path: '/console/display/erp',
              name: 'erp',
              component: () => import('@/pages/console/display/erp/index'),
              meta: {
                requireAuth: true,
                statistic: 'erp',
                showFooter: false
              }
            }
          ]
        },
        {
          path: '/console/workContentSettings',
          name: 'workContentSettings',
          component: () => import('@/pages/console/workContentSettings/index'),
          meta: {
            requireAuth: true,
            statistic: '管理智能体-工作内容设置',
            showFooter: false
          }
        },
        {
          path: '/console/positionSettings',
          name: 'positionSettings',
          component: () => import('@/pages/console/workContentSettings/positionSettings'),
          meta: {
            requireAuth: true,
            statistic: '管理智能体-岗位分配与编辑',
            showFooter: false
          }
        }
      ]
    },
    {
      path: '/console/order',
      name: 'IndexConsoleLeftSide',
      component: DashboardConsole,
      redirect: '/console/order',
      meta: {
        requireAuth: true
      },
      children: [
        {
          path: '/console/order',
          name: 'Order',
          component: () => import('@/pages/console/order/order'),
          meta: {
            requireAuth: true,
            requireGcc: true,
            statistic: '订单列表'
          }
        },
        {
          path: '/console/order/detail',
          name: 'OrderDetail',
          component: () => import('@/pages/console/order/orderDetail'),
          meta: {
            requireAuth: true,
            requireGcc: true,
            statistic: '订单详情'
          }
        },
        {
          path: '/console/orderRenew',
          name: 'OrderRenew',
          component: () => import('@/pages/console/order/orderRenew'),
          meta: {
            requireAuth: true,
            requireGcc: true,
            statistic: '续费管理'
          }
        },
        {
          path: '/console/sureOrderRenew',
          name: 'OrderSureOrderRenew',
          component: () => import('@/pages/console/order/sureOrderRenew'),
          meta: {
            requireAuth: true,
            requireGcc: true,
            statistic: '订单续费'
          }
        },
        {
          path: '/console/orderRecede',
          name: 'OrderRecede',
          component: () => import('@/pages/console/order/orderRecede'),
          meta: {
            requireAuth: true,
            requireGcc: true,
            statistic: '退订管理'
          }
        },
        {
          path: '/console/orderRecede/detail',
          name: 'OrderRecedeDetail',
          component: () => import('@/pages/console/order/orderRecedeDetail'),
          meta: {
            requireAuth: true,
            requireGcc: true,
            statistic: '退订详情'
          }
        },
        {
          path: '/console/wallet',
          name: 'Wallet',
          component: () => import('@/pages/console/wallet'),
          meta: {
            requireAuth: true,
            statistic: '充值管理'
          }
        },
        {
          path: '/console/bills/waterBills',
          name: 'WaterBills',
          component: () => import('@/pages/console/bills/waterBills'),
          meta: {
            requireAuth: true,
            statistic: '流水账单'
          }
        },
        {
          path: '/personal',
          name: 'Personal',
          component: () => import('@/pages/console/personal/index'),
          meta: {
            requireAuth: true,
            statistic: '个人信息'
          }
        },
        {
          path: '/personal/realname',
          name: 'User',
          component: () => import('@/pages/console/personal/realName'),
          meta: {
            requireAuth: true,
            statistic: '实名认证'
          }
        },
        {
          path: '/personal/bank',
          name: 'PersonalBank',
          component: () => import('@/pages/console/personal/bank'),
          meta: {
            requireAuth: true,
            statistic: '银行账户'
          }
        },
        {
          path: '/company',
          name: 'Company',
          component: () => import('@/pages/console/company/index'),
          meta: {
            requireAuth: true,
            statistic: '企业认证'
          }
        },
        {
          path: '/company/detail',
          name: 'CompanyDetail',
          component: () => import('@/pages/console/company/detail'),
          meta: {
            requireAuth: true,
            statistic: '企业信息'
          }
        },
        {
          path: '/company/account',
          name: 'CompanyAccount',
          component: () => import('@/pages/console/company/account'),
          meta: {
            requireAuth: true,
            statistic: '账号信息'
          }
        },
        // 会议纪要
        {
          path: '/company/meeting',
          name: 'CompanyMeeting',
          component: () => import('@/pages/console/company/meeting/index'),
          meta: {
            requireAuth: true,
            statistic: '会议纪要'
          }
        },
        {
          path: '/company/organization',
          name: 'CompanyOrganization',
          component: () => import('@/pages/console/company/organization'),
          meta: {
            requireAuth: true,
            statistic: '组织架构'
          }
        },
        {
          path: '/company/position',
          name: 'CompanyPosition',
          component: () => import('@/pages/console/company/position'),
          meta: {
            requireAuth: true,
            statistic: '企业职位'
          }
        },
        {
          path: '/company/role',
          name: 'CompanyRole',
          component: () => import('@/pages/console/company/role'),
          meta: {
            requireAuth: true,
            statistic: '权限控制'
          }
        },
        {
          path: '/company/information/list',
          name: 'CompanyInformationList',
          component: () => import('@/pages/console/company/information/list'),
          meta: {
            requireAuth: true,
            statistic: '企业消息'
          }
        },
        {
          path: '/company/information/publish',
          name: 'CompanyInformationDetail',
          component: () => import('@/pages/console/company/information/publish'),
          meta: {
            requireAuth: true,
            statistic: '企业消息发布'
          }
        },
        {
          path: '/company/yunpan/list',
          name: 'CompanyYunpanList',
          component: () => import('@/pages/console/company/yunpan/list'),
          meta: {
            requireAuth: true,
            statistic: '企业云盘'
          }
        },
        {
          path: '/company/thirdSave',
          name: 'CompanyThirdSave',
          component: () => import('@/pages/console/company/thirdSave'),
          meta: {
            requireAuth: true,
            statistic: '企业官媒'
          }
        },
        {
          path: '/company/staffReview',
          name: 'CompanyStaffReview',
          component: () => import('@/pages/console/company/staffReview'),
          meta: {
            requireAuth: true,
            statistic: '员工申请'
          }
        },
        {
          path: '/company/developerCertification',
          name: 'DeveloperCertification',
          component: () => import('@/pages/console/company/developerCertification'),
          meta: {
            requireAuth: true,
            statistic: '开发者认证'
          }
        },
        {
          path: '/message',
          name: 'Message',
          component: () => import('@/pages/console/message/index'),
          meta: {
            requireAuth: true,
            statistic: '消息中心'
          }
        },
        {
          path: '/workorder',
          name: 'Workorder',
          component: () => import('@/pages/console/workorder/index'),
          meta: {
            requireAuth: true,
            statistic: '提交工单'
          }
        },
        {
          path: '/workorder/list',
          name: 'WorkorderList',
          component: () => import('@/pages/console/workorder/list'),
          meta: {
            requireAuth: true,
            statistic: '我的工单'
          }
        },
        {
          path: '/workorder/submit',
          name: 'WorkorderSubmit',
          component: () => import('@/pages/console/workorder/submit'),
          meta: {
            requireAuth: true,
            statistic: '工单提交'
          }
        },
        {
          path: '/workorder/detail',
          name: 'WorkorderDetail',
          component: () => import('@/pages/console/workorder/detail'),
          meta: {
            requireAuth: true,
            statistic: '工单详情'
          }
        },
        {
          path: '/console/invoice',
          name: 'Invoice',
          component: () => import('@/pages/console/invoice/index'),
          meta: {
            requireAuth: true,
            requireGcc: true,
            statistic: '发票管理'
          }
        },
        {
          path: '/console/invoice/get',
          name: 'InvoiceGet',
          component: () => import('@/pages/console/invoice/getInvoice'),
          meta: {
            requireAuth: true,
            requireGcc: true,
            statistic: '索要发票'
          }
        },
        {
          path: '/console/invoice/apply',
          name: 'InvoiceApply',
          component: () => import('@/pages/console/invoice/applyInvoice'),
          meta: {
            requireAuth: true,
            requireGcc: true,
            statistic: '申请发票'
          }
        },
        {
          path: '/console/withdrawal',
          name: 'Withdrawal',
          component: () => import('@/pages/console/withdrawal/index'),
          meta: {
            requireAuth: true,
            requireGcc: true,
            statistic: '提现管理'
          }
        },
        {
          path: '/console/withdrawal/apply',
          name: 'WithdrawalApply',
          component: () => import('@/pages/console/withdrawal/applyWithdrawal'),
          meta: {
            requireAuth: true,
            requireGcc: true,
            statistic: '申请提现'
          }
        },
        {
          path: '/console/safe',
          name: 'Safe',
          component: () => import('@/pages/console/safe/index'),
          meta: {
            requireAuth: true,
            requireGcc: true,
            statistic: '安全设置'
          }
        },
        {
          path: '/console/safe/amendPwd',
          name: 'AmendPwd',
          component: () => import('@/pages/console/safe/amendPwd'),
          meta: {
            requireAuth: true,
            requireGcc: true,
            statistic: '修改密码'
          }
        },
        {
          path: '/console/safe/cancelled',
          name: 'Cancelled',
          component: () => import('@/pages/console/safe/cancelled'),
          meta: {
            requireAuth: true,
            requireGcc: true,
            statistic: '申请注销'
          }
        },

        {
          path: '/company/branch/list',
          name: 'BranchList',
          component: () => import('@/pages/console/company/branch/list'),
          meta: {
            requireAuth: true,
            statistic: '分公司'
          }
        },
        {
          path: '/company/log/List',
          name: 'logList',
          component: () => import('@/pages/console/company/log/List'),
          meta: {
            requireAuth: true,
            statistic: '操作日志'
          }
        }
      ]
    },
    {
      path: '/openPlatformHome',
      name: 'openPlatformHome',
      component: OpenPlatformIndex,
      redirect: '/openPlatformHome/home',
      meta: {
        requireAuth: false
      },
      children: [
        {
          path: 'home',
          name: 'home',
          component: () => import('@/pages/openPlatform/Home'),
          meta: {
            requireAuth: false,
            index: 0,
            statistic: '首页'
          }
        },
        {
          path: 'document',
          name: 'document',
          component: () => import('@/pages/openPlatform/Document'),
          redirect: 'document/1/',
          meta: {
            requireAuth: false,
            index: 0,
            statistic: '开发文档'
          },
          children: [
            {
              path: ':id',
              name: 'documentArticle',
              component: () => import('@/pages/openPlatform/Article'),
              meta: {
                requireAuth: false,
                index: 0,
                statistic: '开发文档'
              }
            }
          ]
        },
        {
          path: 'apiCenter',
          name: 'apiCenter',
          component: () => import('@/pages/openPlatform/ApiCenter'),
          redirect: 'apiCenter/1',
          meta: {
            requireAuth: false,
            index: 0,
            statistic: 'api中心'
          },
          children: [
            {
              path: ':id',
              name: 'apiCenterArticle',
              component: () => import('@/pages/openPlatform/Article'),
              meta: {
                requireAuth: false,
                index: 0,
                statistic: 'api中心'
              }
            }
          ]
        }
      ]
    },
    {
      path: '/landingPage',
      name: 'landingPage',
      component: LandingIndex,
      redirect: '/landingPage/applyExperience',
      meta: {
        requireAuth: false,
        index: 0,
        statistic: '落地页'
      },
      children: [
        {
          path: '/landingPage/applyExperience',
          name: 'applyExperience',
          component: () => import('@/pages/landingPage/ApplyExperience'),
          meta: {
            requireAuth: false,
            index: 0,
            statistic: '资海云-申请体验',
            title: '资海云-申请体验'
          }
        },
        {
          path: '/landingPage/applyAgent',
          name: 'applyAgent',
          component: () => import('@/pages/landingPage/ApplyAgent'),
          meta: {
            requireAuth: false,
            index: 0,
            statistic: '资海云-申请代理',
            title: '资海云-申请代理'
          }
        }
      ]
    },
    {
      path: '/aiChat',
      name: 'aiChat', // 人工智能客服
      component: DashboardConsole,
      redirect: '/aiChat/chat',
      meta: {
        requireAuth: true,
        statistic: '人工智能客服',
        title: '人工智能客服'
      },
      children: [
        {
          path: '/aiChat/chat',
          name: 'chat',
          component: () => import('@/pages/aiChat/index'),
          meta: {
            requireAuth: true,
            statistic: '人工智能客服',
            title: '人工智能客服',
            showFooter: false
          }
        }
      ]
    },
    // 名片管理
    {
      path: '/cards',
      name: 'cards',
      component: DashboardConsole,
      redirect: '/cards/index',
      meta: {
        requireAuth: true,
        statistic: '名片管理',
        title: '名片管理',
      },
      children: [
        {
          path: '/cards/index',
          name: 'cardsIndex',
          component: () => import('@/pages/dashboard/components/IframeView'),
          meta: {
            requireAuth: true,
            statistic: '名片管理',
            title: '名片管理',
            showFooter: false,
            pathType: 'iframe',
            iframeUrl: 'https://card.china9.cn/#/home'
          }
        }
      ]
    },
    // 新控制台
    {
      path: '/dashboard',
      name: 'dashboard', // 控制台
      component: () => import('@/pages/dashboard/index'),
      redirect: '/dashboard/home',
      meta: {
        requireAuth: true,
        statistic: '控制台',
        title: '控制台'
      },
      children: [
        {
          path: '/dashboard/home',
          name: 'dashboardHome',
          component: () => import('@/pages/dashboard/home/<USER>'),
          meta: {
            requireAuth: true,
            statistic: '控制台',
            title: '控制台',
            icon: require('@/assets/image/dashboard/layout/home.png'),
            activeIcon: require('@/assets/image/dashboard/layout/home-active.png'),
            showFooter: false,
            pathType: 'page'
          }
        },
        {
          path: '/dashboard/aigc',
          name: 'dashboardAigc',
          component: () => import('@/pages/dashboard/components/IframeView'),
          meta: {
            requireAuth: true,
            statistic: 'AIGC',
            title: 'AIGC',
            icon: require('@/assets/image/dashboard/layout/aigc.png'),
            activeIcon: require('@/assets/image/dashboard/layout/aigc-active.png'),
            showFooter: false,
            pathType: 'iframe',
            iframeUrl: 'https://dev.china9.cn/aigc_dev/#/'
          }
        },
        {
          path: '/dashboard/jzt',
          name: 'dashboardJzt',
          component: () => import('@/pages/dashboard/components/IframeView'),
          meta: {
            requireAuth: true,
            statistic: '建站通',
            title: '建站通',
            icon: require('@/assets/image/dashboard/layout/jzt.png'),
            activeIcon: require('@/assets/image/dashboard/layout/jzt-active.png'),
            showFooter: false,
            pathType: 'iframe',
            // iframeUrl: 'https://zhjzt.china9.cn/jzt_all/'
            iframeUrl: 'https://jzt_dev_1.china9.cn/jzt_all/'
          }
        },
        { // 电商通
          path: '/dashboard/ecommerce',
          name: 'dashboardEcommerce',
          component: () => import('@/pages/dashboard/components/IframeView'),
          meta: {
            requireAuth: true,
            statistic: '电商通',
            title: '电商通',
            icon: require('@/assets/image/dashboard/layout/ecommerce.png'),
            activeIcon: require('@/assets/image/dashboard/layout/ecommerce-active.png'),
            showFooter: false,
            pathType: 'iframe',
            iframeUrl: 'https://siyuds.zihaiwangluo.com/admin/login',
            params: [
              { key: 'code', value: 'user_uid' }
            ]
          }
        },
        {
          path: '/dashboard/sale',
          name: 'dashboardSale',
          component: () => import('@/pages/dashboard/components/IframeView'),
          meta: {
            requireAuth: true,
            statistic: '销售通',
            title: '销售通',
            icon: require('@/assets/image/dashboard/layout/sale.png'),
            activeIcon: require('@/assets/image/dashboard/layout/sale-active.png'),
            showFooter: false,
            pathType: 'iframe',
            iframeUrl: '',
            params: [
              { key: 'code', value: 'user_uid' }
            ]
          },
        },
        {
          path: '/dashboard/ygt',
          name: 'dashboardYgt',
          redirect: '/dashboard/ygt/home',
          component: () => import('@/pages/dashboard/components/RouterView'),
          meta: {
            requireAuth: true,
            statistic: '易管通',
            title: '易管通',
            icon: require('@/assets/image/dashboard/layout/ygt.png'),
            activeIcon: require('@/assets/image/dashboard/layout/ygt-active.png'),
            showFooter: false,
            pathType: 'iframe',
            iframeUrl: '',
            params: [
              { key: 'code', value: 'user_uid' }
            ]
          },
          children: [
            {
              path: '/dashboard/ygt/home',
              name: 'dashboardYgtHome',
              component: () => import('@/pages/dashboard/ygt/index'),
              meta: {
                requireAuth: true,
                statistic: '易管通',
                title: '易管通',
                showFooter: false,
                pathType: 'page'
              }
            },
            {
              path: '/dashboard/ygt/ai-boss',
              name: 'dashboardAiBoss',
              component: () => import('@/pages/console/workContentSettings'),
              meta: {
                requireAuth: true,
                statistic: 'AI老板',
                title: 'AI老板',
                showFooter: false,
                pathType: 'page',
                params: [
                  // { key: 'code', value: 'user_uid' }
                ]
              }
            },
            {
              path: '/dashboard/ygt/administration',
              name: 'dashboardAdministration',
              component: () => import('@/pages/dashboard/components/IframeView'),
              meta: {
                requireAuth: true,
                statistic: '行政管理',
                title: '行政管理',
                showFooter: false,
                pathType: 'iframe',
                iframeUrl: 'https://ihr.china9.cn/human/main/index/v/ygt#/main/console',
                params: [
                  // { key: 'code', value: 'user_uid' }
                ]
              }
            },
            {
              path: '/dashboard/ygt/hr',
              name: 'dashboardHr',
              component: () => import('@/pages/dashboard/components/IframeView'),
              meta: {
                requireAuth: true,
                statistic: '人资管理',
                title: '人资管理',
                showFooter: false,
                pathType: 'iframe',
                iframeUrl: 'https://ihr.china9.cn/human/main/index/v/ygt#/main/console',
                params: [
                  // { key: 'code', value: 'user_uid' }
                ]
              }
            },
            {
              path: '/dashboard/ygt/legal',
              name: 'dashboardLegal',
              component: () => import('@/pages/dashboard/components/IframeView'),
              meta: {
                requireAuth: true,
                statistic: '法务管理',
                title: '法务管理',
                showFooter: false,
                pathType: 'iframe',
                iframeUrl: 'https://ihr.china9.cn/human/main/index/v/ygt#/main/console',
                params: [
                  // { key: 'code', value: 'user_uid' }
                ]
              }
            },
            {
              path: '/dashboard/ygt/finance',
              name: 'dashboardFinance',
              component: () => import('@/pages/dashboard/components/IframeView'),
              meta: {
                requireAuth: true,
                statistic: '财务管理',
                title: '财务管理',
                showFooter: false,
                pathType: 'iframe',
                iframeUrl: 'https://ihr.china9.cn/human/main/index/v/ygt#/main/console',
                params: [
                  // { key: 'code', value: 'user_uid' }
                ]
              }
            }
          ]
        },
        {
          path: '/dashboard/app',
          name: 'dashboardApp',
          component: () => import('@/pages/dashboard/app/index'),
          meta: {
            requireAuth: true,
            statistic: '三方应用',
            title: '三方应用',
            icon: require('@/assets/image/dashboard/layout/app.png'),
            activeIcon: require('@/assets/image/dashboard/layout/app-active.png'),
            showFooter: false,
            pathType: 'iframe',
            iframeUrl: '',
            params: [
              { key: 'code', value: 'user_uid' }
            ]
          },
        },
        {
          path: '/dashboard/settings',
          name: 'dashboardSettings',
          component: () => import('@/pages/dashboard/components/RouterView'),
          redirect: '/company/detail',
          meta: {
            requireAuth: true,
            statistic: '系统设置',
            title: '系统设置',
            icon: require('@/assets/image/dashboard/layout/settings.png'),
            activeIcon: require('@/assets/image/dashboard/layout/settings-active.png'),
            showFooter: false,
            pathType: 'iframe',
            iframeUrl: '',
            params: [
              { key: 'code', value: 'user_uid' }
            ]
          }
        }
      ]
    }
  ]
})

const needForBaiduStatistics = ['/home']

router.beforeEach(async (to, from, next) => {
  window.scrollTo(0, 0)
  // console.log('requireAuth:', to.meta.requireAuth)
  // 统计上报
  // if (to.meta.statistic) {
  //   if (process.env.NODE_ENV === 'production') {
  //     gatherVisit({app_key: 'ba1a9e62021f2233265aef31e4f15483bd786d89', ip: '*************', name: to.meta.statistic})
  //   }
  // }

  if (needForBaiduStatistics.includes(to.path)) {
    let script = document.createElement('script')
    script.id = 'baidu-statistics'
    script.setAttribute('src', 'https://hm.baidu.com/hm.js?0dbb4f63a579d2d7a009fa1df830affd')
    document.head.appendChild(script)
  } else {
    let script = document.getElementById('baidu-statistics')
    if (script) {
      document.head.removeChild(script)
      let baiduAlert = document.getElementById('aff-im-root')
      if (baiduAlert) {
        baiduAlert.parentNode.removeChild(baiduAlert)
      }
    }
  }

  if (to.meta.requireAuth) {
    var token = Cookies.get('token') || Cookies.get('access_token') || store.getters.token

    if (token) {
      // 如果to.path以‘/console/display’开头
      // if ((to.path.startsWith('/console/display') && !to.path.startsWith('/console/display/console')) || to.path.startsWith('/company')) {
      if ((to.path.startsWith('/console/display') && !to.path.startsWith('/console/display/console'))) {
        if (!store.getters.menuList) {
          await store.dispatch('getDynamicRouter')
        }
        if (!store.getters.buyGoodsList) {
          await store.dispatch('getCompanyProductList')
        }

        const selectedMenu = store.getters.menuFlatList.find(item => item.path === to.path)
        if (selectedMenu) {
          const {url, purchase, click, popup_success, popup_error, type, popup} = selectedMenu
          if (!purchase) {
            BuyDialogPopup.install({toRoute: to})
            Message.error('您还未购买该产品');
            return next(from.path)
          }

          if (!click) {
            if (type === 'url') {
              return window.open(url, '_blank')
            } else {
              if (popup_success && popup_error) {
                return MessageBox(popup || '您暂无权限查看该页面，请联系客服', '提示', {
                  distinguishCancelAndClose: true,
                  confirmButtonText: '确定',
                  cancelButtonText: '取消'
                }).then(() => {
                  if (popup_success.includes('http://') || popup_success.includes('https://')) {
                    window.open(popup_success, '_blank')
                  }/* else{
                    Message.success(popup_success);
                  } */
                }).catch(() => {
                  if (popup_error.includes('http://') || popup_error.includes('https://')) {
                    window.open(popup_error, '_blank')
                  }/* else{
                    Message.success(popup_error);
                  } */
                })
              } else if (popup_success) {
                return MessageBox(popup || '您暂无权限查看该页面，请联系客服', '提示').then(() => {
                  if (popup_success.includes('http://') || popup_success.includes('https://')) {
                    window.open(popup_success, '_blank')
                  }/* else{
                    Message.success(popup_success);
                  } */
                })
              } else {
                Message.error(popup || '您暂无权限查看该页面，请联系客服')
                return next(from.path)
              }
            }
          } else {
            const jztUrl = ['/console/display/publicize', '/console/display/website']
            if (jztUrl.includes(to.path)) {
              if (!store.getters.jztSiteInfo || !store.getters.jztSiteInfo.id) {
                try {
                  await store.dispatch('getSiteList')
                } catch (e) {
                  Message.error(e.msg || e.message)
                }
              }
              if (!store.getters.jztUserRole) {
                await store.dispatch('getJztUserAuth')
              }
            }
          }
        }
        next()
      } else {
        next()
      }
    } else {
      // Vue.prototype.bus.$emit("loginHud",{
      //   show:true
      // })
      window.location.href = process.env.ACCOUNT_API + '/login.html?oauth_callback=http://' + process.env.VUE_DOMAIN + encodeURIComponent('/#' + to.fullPath)
    }
  } else {
    next()
  }
})

export default router
