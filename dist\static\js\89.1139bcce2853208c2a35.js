webpackJsonp([89],{NNQ7:function(t,a,e){"use strict";Object.defineProperty(a,"__esModule",{value:!0});var n=e("cMGX"),o=e("pI5c"),i=e("rSnQ"),s={name:"order",components:{Pagination:n.a,payComponent:i.a},data:function(){return{tableLoading:!1,data:{},infoData:{no:"",type:0},no:""}},created:function(){this.no=this.$route.query.no,this.getDetail()},methods:{getDetail:function(){var t=this;this.tableLoading=!0,Object(o._15)({no:this.no}).then(function(a){t.tableLoading=!1,t.data=a.data}).catch(function(){t.tableLoading=!1})},cancel:function(){var t=this;this.$confirm("取消该订单, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){Object(o._13)({no:t.no}).then(function(a){t.$message.success("订单取消成功"),t.$router.go(-1)})})},destory:function(){var t=this;this.$confirm("取消该订单, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){Object(o._13)({no:t.no}).then(function(a){t.$message.success("订单取消成功"),t.$router.go(-1)})})},showDetail:function(){},pay:function(){this.data&&(this.data.amount=this.data.money,this.data.order_no=this.data.no,this.$router.push({path:"/pay",query:{no:this.no}}))}}},r={render:function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",{staticClass:"app-container"},[e("el-card",{staticClass:"box-card",attrs:{"v-loading":t.tableLoading}},[e("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[e("span",[t._v("订单管理")])]),t._v(" "),e("el-form",{directives:[{name:"loading",rawName:"v-loading",value:t.tableLoading,expression:"tableLoading"}],staticClass:"demo-form-inline",attrs:{inline:!0,model:t.data,size:"small"}},[e("div",{staticClass:"container"},[e("el-form-item",{attrs:{label:"产品名称:",prop:"title"}},[t._v(t._s(t.data.title))]),t._v(" "),e("el-form-item",{attrs:{label:"产品配置:",prop:"desc"}},[t._v(t._s(t.data.desc))]),t._v(" "),e("el-form-item",{attrs:{label:"购买方式:"}},[t._v("周期时长")]),t._v(" "),e("el-form-item",{attrs:{label:"购买价格:",prop:"money"}},[t._v(t._s(t.data.money))]),t._v(" "),e("el-form-item",{attrs:{label:"时长:",prop:"month"}},[t._v(t._s(t.data.month))]),t._v(" "),e("el-form-item",{attrs:{label:"下单时间:",prop:"month"}},[t._v(t._s(t.data.time))]),t._v(" "),e("el-form-item",{attrs:{label:""}}),t._v(" "),e("el-form-item",{attrs:{label:"支付方式:",prop:"pay_type"}},[t._v(t._s(t.data.pay_type))]),t._v(" "),e("el-form-item",{attrs:{label:"支付状态:",prop:"status"}},[t._v(t._s(t.data.status))])],1)]),t._v(" "),"待付款"===t.data.status?e("el-button",{attrs:{type:"primary"},on:{click:function(a){return t.pay()}}},[t._v("去付款")]):t._e(),t._v(" "),"待付款"===t.data.status?e("el-button",{attrs:{type:"danger"},on:{click:function(a){return t.cancel()}}},[t._v("取消订单")]):t._e(),t._v(" "),"已超时"===t.data.status?e("el-button",{attrs:{type:"danger"},on:{click:function(a){return t.destory()}}},[t._v("删除订单")]):t._e()],1),t._v(" "),e("payComponent",{ref:"payComponent"})],1)},staticRenderFns:[]};var l=e("VU/8")(s,r,!1,function(t){e("UUqi")},"data-v-3926ebd9",null);a.default=l.exports},UUqi:function(t,a){}});