webpackJsonp([113],{Mgym:function(t,i){},fLMA:function(t,i,e){"use strict";Object.defineProperty(i,"__esModule",{value:!0});var r={render:function(){var t=this,i=t.$createElement,e=t._self._c||i;return e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticStyle:{"min-height":"500px"}},[e("el-col",{attrs:{xxl:16,xl:18,lg:24,md:24,sm:24,xs:24}},[!t.loading&&t.profile?e("table",{staticClass:"profile_table",staticStyle:{width:"100%"},attrs:{cellpadding:"0",border:"1",cellspacing:"0"}},[e("tr",[e("td",[t._v("姓名")]),t._v(" "),e("td",{attrs:{id:"name"}},[t._v(t._s(t.profile.name))]),t._v(" "),e("td",[t._v("性别")]),t._v(" "),e("td",{attrs:{id:"sex"}},[t._v(t._s(t.profile.sex))]),t._v(" "),e("td",{attrs:{rowspan:"3",align:"center"}},[e("img",{staticStyle:{width:"120px",height:"auto"},attrs:{src:t.profile.avatar,id:"avatar",alt:"暂无照片"}})])]),t._v(" "),e("tr",[e("td",[t._v("出生日期")]),t._v(" "),e("td",{attrs:{id:"Birthday"}},[t._v(t._s(t.profile.Birthday))]),t._v(" "),e("td",[t._v("民族")]),t._v(" "),e("td",{attrs:{id:"Nation"}},[t._v(t._s(t.profile.Nation))])]),t._v(" "),e("tr",[e("td",[t._v("身份证号")]),t._v(" "),e("td",{attrs:{colspan:"3",id:"numberid"}},[t._v(t._s(t.profile.numberid))])]),t._v(" "),e("tr",[e("td",[t._v("政治面貌")]),t._v(" "),e("td",{attrs:{colspan:"2",id:"Politics"}},[t._v(t._s(t.profile.Politics))]),t._v(" "),e("td",[t._v("婚否")]),t._v(" "),e("td",{attrs:{id:"marriage"}},[t._v(t._s(t.profile.marriage))])]),t._v(" "),e("tr",[e("td",[t._v("籍贯")]),t._v(" "),e("td",{attrs:{colspan:"2",id:"Nativeplace"}},[t._v(t._s(t.profile.Nativeplace))]),t._v(" "),e("td",[t._v("血型")]),t._v(" "),e("td",{attrs:{id:"Blood"}},[t._v(t._s(t.profile.Blood))])]),t._v(" "),e("tr",[e("td",[t._v("手机")]),t._v(" "),e("td",{attrs:{colspan:"2",id:"phone"}},[t._v(t._s(t.profile.phone))]),t._v(" "),e("td",[t._v("学历")]),t._v(" "),e("td",{attrs:{id:"education"}},[t._v(t._s(t.profile.education))])]),t._v(" "),e("tr",[e("td",[t._v("邮箱")]),t._v(" "),e("td",{attrs:{colspan:"2",id:"email"}},[t._v(t._s(t.profile.email))]),t._v(" "),e("td",[t._v("身高")]),t._v(" "),e("td",{attrs:{id:"height"}},[t._v(t._s(t.profile.height))])]),t._v(" "),t.profile.permission?e("tr",{staticClass:"permission"},[e("td",[t._v("入职日期")]),t._v(" "),e("td",{attrs:{colspan:"2",id:"entry_date"}},[t._v(t._s(t.profile.entry_date))]),t._v(" "),e("td",[t._v("工龄")]),t._v(" "),e("td",{attrs:{id:"workingYears"}},[t._v(t._s(t.profile.workingYears))])]):t._e()]):t._e(),t._v(" "),!t.loading&&t.profile?e("table",{staticClass:"profile_table",staticStyle:{width:"100%","margin-top":"20px"},attrs:{cellpadding:"0",border:"1",cellspacing:"0"}},[e("colgroup",[e("col",{staticStyle:{width:"20%"}}),t._v(" "),e("col",{staticStyle:{width:"80%"}})]),t._v(" "),t.profile.permission?e("tr",{staticClass:"permission"},[e("td",[t._v("所属部门")]),t._v(" "),e("td",{attrs:{id:"department"}},[t._v(t._s(t.profile.department))])]):t._e(),t._v(" "),t.profile.permission?e("tr",{staticClass:"permission"},[e("td",[t._v("职位")]),t._v(" "),e("td",{attrs:{id:"position"}},[t._v(t._s(t.profile.position))])]):t._e(),t._v(" "),e("tr",[e("td",[t._v("户口所在地")]),t._v(" "),e("td",{attrs:{id:"Registered"}},[t._v(t._s(t.profile.Registered))])]),t._v(" "),e("tr",[e("td",[t._v("现住址")]),t._v(" "),e("td",{attrs:{id:"Currentaddress"}},[t._v(t._s(t.profile.Currentaddress))])]),t._v(" "),e("tr",[e("td",[t._v("专长")]),t._v(" "),e("td",{attrs:{id:"Expertise"}},[t._v(t._s(t.profile.Expertise))])])]):t._e()])],1)},staticRenderFns:[]};var a=e("VU/8")({name:"ArchivesProfile",props:["data"],data:function(){return{loading:!1}},computed:{profile:function(){return this.data&&this.data.info?(this.loading=!1,this.data.info):null}}},r,!1,function(t){e("Mgym")},"data-v-56fd2038",null);i.default=a.exports}});