webpackJsonp([101],{"+j7p":function(t,a,e){"use strict";Object.defineProperty(a,"__esModule",{value:!0});var s=e("xO/y"),n={name:"newsDetail",data:function(){return{data:{}}},created:function(){this.getData()},methods:{getData:function(){var t=this;Object(s.g)({id:this.$route.query.id}).then(function(a){t.data=a.data})},last:function(){this.$router.replace({query:{id:this.data.last.id},path:"/news/detail"}),this.$router.go(0)},next:function(){this.$router.replace({query:{id:this.data.next.id},path:"/news/detail"}),this.$router.go(0)}}},i={render:function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",{staticClass:"container"},[e("div",{staticClass:"w1200"},[e("div",{staticClass:"body"},[e("h1",[t._v(t._s(t.data.title))]),t._v(" "),e("div",{staticClass:"author"},[e("span",[t._v("发布作者：资海云")]),t._v(" "),e("span",[t._v("浏览：1464")]),t._v(" "),e("span",[t._v("时间："+t._s(t.data.publish_at))])]),t._v(" "),e("div",{staticClass:"content"},[e("p",{domProps:{innerHTML:t._s(t.data.content)}})])]),t._v(" "),e("div",{staticClass:"footer"},[e("div",[t.data.last?e("span",{on:{click:t.last}},[t._v("上一条："+t._s(t.data.last.title))]):e("span"),t._v(" "),t.data.next?e("span",{on:{click:t.next}},[t._v("下一条："+t._s(t.data.next.title))]):e("span")])])])])},staticRenderFns:[]};var d=e("VU/8")(n,i,!1,function(t){e("0BB/")},"data-v-0f98f144",null);a.default=d.exports},"0BB/":function(t,a){}});