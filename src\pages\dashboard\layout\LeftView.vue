<template>
  <div class="left-wrapper">
    <div class="nav-wrapper">
      <div :class="['nav-item', { 'active': isActive(item) }]" v-for="(item, index) in leftNav" @click="toPath(item)">
        <div class="img-box">
          <img class="icon" :src="item.icon" alt="">
          <img class="icon-a" :src="item.iconA" alt="">
        </div>
        <span>{{ item.statistic }}</span>
      </div>
    </div>
    <!-- 设置菜单显示 -->
    <div class="setting-btn" @click="showSettingMenu">
      <div class="setting-icon">
        <div class="dot-grid">
          <div class="dot" v-for="i in 9" :key="i"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState, mapActions } from 'vuex'
import { findChildren } from '@/utils/index'
import zhGoods from '@/pages/console/display/mixins/zhGoods'

export default {
  name: 'LeftView',
  data() {
    return {
      // leftNav: []
    }
  },
  mixins: [zhGoods],
  computed: {
    ...mapState(['dashboardMenus']),
    leftNav() {
      console.log('dashboardMenus', this.dashboardMenus)
      return this.dashboardMenus || []
    }
  },
  created() {
    // this.getRouters()
    this.getDashboardMenus()
  },
  methods: {
    ...mapActions(['getDashboardMenus']),
    getRouters() {
      const router = this.$router.options.routes;
      let leftNav = [];
      router.forEach(item => {
        if (item.name == 'dashboard') {
          leftNav = item.children || [];
        }
      })
      // console.log('leftNav', leftNav)
      this.leftNav = leftNav;
    },
    // 当前菜单是否选中
    isActive(item) {
      return item.name == this.$route.name || findChildren(item).includes(this.$route.path) || this.$route.path.includes(item.path)
    },
    // 跳转
    toPath(item) {
      if (item.path === this.$route.path) return;
      this.menuToPage(item)
    },
    // 显示设置菜单
    showSettingMenu() {
      console.log('显示设置菜单')
      // 这里可以添加显示设置菜单的逻辑
      // 比如弹出菜单、跳转到设置页面等
    }
  }
}
</script>

<style scoped lang="scss">
.left-wrapper {
  width: 76px;
  height: 100%;
  padding-top: 10px;
  padding-bottom: 20px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;

  .nav-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    overflow-y: auto;
    //不显示滚动条但是可以滚动
    &::-webkit-scrollbar {
      display: none;
    }

    .nav-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 10px 0;
      cursor: pointer;
      width: 100%;
      margin-bottom: 6px;

      .img-box {
        position: relative;
        width: 20px;
        height: 20px;
        img {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          display: block;
        }

        .icon-a {
          display: none;
        }
      }

      span {
        margin-top: 6px;
        font-size: 14px;
        color: #333333;
      }

      &.active {
        background: transparent;
        .icon {
          display: none;
        }

        .icon-a {
          display: block;
        }
        span {
          color: #4D80FF;
        }
      }
    }
  }

  .setting-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 10px 0;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      .dot {
        // 第一排和第三排悬停时变深
        &:nth-child(1), &:nth-child(2), &:nth-child(3),
        &:nth-child(7), &:nth-child(8), &:nth-child(9) {
          background-color: #4D80FF;
        }

        // 第二排悬停时变深
        &:nth-child(4), &:nth-child(5), &:nth-child(6) {
          background-color: #4D80FF;
        }
      }
    }

    .setting-icon {
      display: flex;
      justify-content: center;
      align-items: center;

      .dot-grid {
        display: grid;
        place-items: center;
        grid-template-columns: repeat(3, 1fr);
        grid-template-rows: repeat(3, 1fr);
        gap: 3px;
        width: 20px;
        height: 20px;

        .dot {
          width: 4px;
          height: 4px;
          border-radius: 50%;
          transition: background-color 0.3s ease;

          // 第一排和第三排 (1,2,3,7,8,9)
          &:nth-child(1), &:nth-child(2), &:nth-child(3),
          &:nth-child(7), &:nth-child(8), &:nth-child(9) {
            background-color: #797E84;
          }

          // 第二排 (4,5,6)
          &:nth-child(4), &:nth-child(5), &:nth-child(6) {
            background-color: #A6ADB4;
          }
        }
      }
    }
  }
}
@media screen and (max-width: 1366px) {
  .left-wrapper {
    .nav-wrapper {
      .nav-item {
        padding: 6px 0;
        .img-box {
          width: 16px;
          height: 16px;
        }
        span {
          font-size: 12px;
        }
      }
    }

    .setting-btn {
      padding: 6px 0;

      .setting-icon {
        .dot-grid {
          width: 16px;
          height: 16px;
          gap: 1px;

          .dot {
            width: 3px;
            height: 3px;
          }
        }
      }
    }
  }
}
</style>