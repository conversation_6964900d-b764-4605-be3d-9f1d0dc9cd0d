webpackJsonp([69],{"3Wah":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var l=a("Dd8w"),n=a.n(l),r=a("vLgD");var i={name:"LogList",data:function(){return{query:{phone:"",keywords:"",date:[]},tableData:[],page:{page:1,limit:10},loading:!1,total:0}},methods:{getList:function(e,t){var a=this;e&&(this.page.page=e),t&&(this.page.limit=t);var l=n()({},this.query,this.page);this.loading=!0,function(e){return Object(r.a)({url:"/api/operationlogs/index",method:"POST",data:e})}(l).then(function(e){console.log(e),200===e.code&&e.data&&(e.data.data&&(a.tableData=e.data.data),a.total=e.data.total)}).finally(function(){a.loading=!1})},handleSizeChange:function(e){this.page.limit=e,this.getList(null,e)}},mounted:function(){this.getList(1)}},s={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-card",[a("div",{staticClass:"clearfix center-flex",attrs:{slot:"header"},slot:"header"},[a("span",[e._v("操作日志")])]),e._v(" "),a("div",{staticClass:"content-wrap"},[a("div",{staticStyle:{"text-align":"right"}},[a("el-form",{attrs:{model:e.query,inline:!0}},[a("el-form-item",[a("el-input",{attrs:{placeholder:"请入操作账号",size:"small",clearable:""},model:{value:e.query.phone,callback:function(t){e.$set(e.query,"phone",t)},expression:"query.phone"}})],1),e._v(" "),a("el-form-item",[a("el-input",{attrs:{placeholder:"请输入操作内容",size:"small",clearable:""},model:{value:e.query.keywords,callback:function(t){e.$set(e.query,"keywords",t)},expression:"query.keywords"}})],1),e._v(" "),a("el-form-item",[a("el-date-picker",{staticStyle:{width:"300px"},attrs:{type:"daterange",format:"yyyy-MM-dd","value-format":"yyyy-MM-dd","range-separator":"至","start-placeholder":"开始日期",size:"small","end-placeholder":"结束日期"},model:{value:e.query.date,callback:function(t){e.$set(e.query,"date",t)},expression:"query.date"}})],1),e._v(" "),a("el-form-item",[a("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(t){return e.getList(1)}}},[e._v("检索")])],1)],1)],1),e._v(" "),a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"list-wrap",staticStyle:{"margin-top":"30px"}},[a("el-table",{staticStyle:{width:"100%"},attrs:{data:e.tableData}},[a("el-table-column",{attrs:{prop:"phone",label:"操作账号",align:"center"}}),e._v(" "),a("el-table-column",{attrs:{prop:"name",label:"操作人",align:"center"}}),e._v(" "),a("el-table-column",{attrs:{prop:"content",label:"操作内容",align:"center","min-width":"200"}}),e._v(" "),a("el-table-column",{attrs:{prop:"ip",label:"操作IP",align:"center"}}),e._v(" "),a("el-table-column",{attrs:{prop:"created_at",label:"操作时间",align:"center"}})],1),e._v(" "),a("div",{staticClass:"page-wrap",staticStyle:{"margin-top":"40px","text-align":"center"}},[a("el-pagination",{attrs:{background:"","current-page":e.page.page,"page-sizes":[10,20,30,40],"page-size":e.page.limit,layout:"total, sizes, prev, pager, next, jumper",total:e.total},on:{"size-change":e.handleSizeChange,"current-change":e.getList,"update:currentPage":function(t){return e.$set(e.page,"page",t)},"update:current-page":function(t){return e.$set(e.page,"page",t)}}})],1)],1)])])],1)},staticRenderFns:[]};var o=a("VU/8")(i,s,!1,function(e){a("DYVF")},"data-v-7fdfd7d6",null);t.default=o.exports},DYVF:function(e,t){}});