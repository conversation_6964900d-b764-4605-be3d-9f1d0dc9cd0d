webpackJsonp([67],{"33MU":function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var s=a("cMGX"),l=a("wU6q"),i=a("+PoZ"),r={name:"list",components:{Pagination:s.a,CardWrap:l.a},data:function(){return{tableLoading:!1,tableData:[],cateList:[],total:0,queryList:{page:1,perPage:10}}},created:function(){this.getCateList(),this.getList()},methods:{getCateList:function(){var t=this;Object(i.d)(this.queryList).then(function(e){t.cateList=e.data}).catch(function(){})},getList:function(){var t=this;this.tableLoading=!0,Object(i.g)(this.queryList).then(function(e){t.tableLoading=!1,e.data.data.forEach(function(t){0===t.status?t.status="待回复":1===t.status?t.status="正在处理中":2===t.status?t.status="已回复":3===t.status&&(t.status="已关闭")}),t.tableData=e.data.data,t.total=e.data.total}).catch(function(){t.tableLoading=!1})},showDetail:function(t){this.$router.push({path:"/workorder/detail",query:{id:t.id}})},destory:function(t){var e=this;this.$confirm("删除该工单, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){Object(i.e)({id:t.id}).then(function(t){e.$message.success(t.message),e.getList()})})}}},n={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-container"},[a("card-wrap",{attrs:{title:"我的工单"}},[a("div",{staticClass:"container-wrapper"},[a("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:t.queryList,size:"small"}},[a("el-form-item",{attrs:{label:"工单内容"}},[a("el-input",{attrs:{placeholder:"工单标题",clearable:""},model:{value:t.queryList.question,callback:function(e){t.$set(t.queryList,"question",e)},expression:"queryList.question"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"工单类型"}},[a("el-select",{attrs:{placeholder:"请选择"},model:{value:t.queryList.cate_id,callback:function(e){t.$set(t.queryList,"cate_id",e)},expression:"queryList.cate_id"}},t._l(t.cateList,function(t,e){return a("el-option",{key:e,attrs:{label:t.name,value:t.id}})}),1)],1),t._v(" "),a("el-form-item",{attrs:{label:"工单状态"}},[a("el-select",{attrs:{placeholder:"全部",clearable:""},model:{value:t.queryList.status,callback:function(e){t.$set(t.queryList,"status",e)},expression:"queryList.status"}},[a("el-option",{attrs:{label:"待回复",value:0}}),t._v(" "),a("el-option",{attrs:{label:"正在处理中",value:1}}),t._v(" "),a("el-option",{attrs:{label:"已回复",value:2}}),t._v(" "),a("el-option",{attrs:{label:"已关闭",value:3}})],1)],1),t._v(" "),a("el-form-item",{attrs:{label:"提交时间"}},[a("el-date-picker",{attrs:{"value-format":"yyyy-MM-dd",type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:t.queryList.created_at,callback:function(e){t.$set(t.queryList,"created_at",e)},expression:"queryList.created_at"}})],1),t._v(" "),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:t.getList}},[t._v("搜索")])],1)],1),t._v(" "),a("div",{staticClass:"table"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.tableLoading,expression:"tableLoading"}],staticStyle:{width:"100%"},attrs:{data:t.tableData,height:"100%",stripe:""}},[a("el-table-column",{attrs:{prop:"work_num",label:"工单编号"}}),t._v(" "),a("el-table-column",{attrs:{prop:"content",label:"工单内容"}}),t._v(" "),a("el-table-column",{attrs:{prop:"category",label:"工单类型"}}),t._v(" "),a("el-table-column",{attrs:{prop:"created_at",label:"提交时间"}}),t._v(" "),a("el-table-column",{attrs:{prop:"status",label:"工单状态"}}),t._v(" "),a("el-table-column",{attrs:{label:"操作"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{staticStyle:{color:"#3a8ee6"},attrs:{type:"text"},on:{click:function(a){return t.showDetail(e.row)}}},[t._v("查看")]),t._v(" "),"已关闭"===e.row.status?a("el-button",{staticStyle:{color:"red"},attrs:{type:"text"},on:{click:function(a){return t.destory(e.row)}}},[t._v("删除")]):t._e()]}}])})],1)],1),t._v(" "),a("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],staticStyle:{"text-align":"center"},attrs:{total:t.total,page:t.queryList.page,limit:t.queryList.perPage},on:{"update:page":function(e){return t.$set(t.queryList,"page",e)},"update:limit":function(e){return t.$set(t.queryList,"perPage",e)},pagination:t.getList}})],1)])],1)},staticRenderFns:[]};var o=a("VU/8")(r,n,!1,function(t){a("sPG1")},"data-v-9267914e",null);e.default=o.exports},sPG1:function(t,e){}});