webpackJsonp([34],{"E+5q":function(t,e){},Kkxl:function(t,e){},O2Qw:function(t,e){},bHGr:function(t,e,i){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var a={render:function(){this.$createElement;this._self._c;return this._m(0)},staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"app-banner"},[e("img",{staticClass:"h-banner",attrs:{src:i("Kb2M"),alt:""}}),this._v(" "),e("div",{staticClass:"banner-content"},[e("div",{staticClass:"left-content"},[e("img",{staticClass:"app-icon",attrs:{src:i("9Zmw"),alt:""}}),this._v(" "),e("div",{staticClass:"app-content-wrap"},[e("h3",{staticClass:"b-title"},[e("span",[this._v("三方应用")]),this._v("自由集成，一键添加您的专属网址与工具")]),this._v(" "),e("p",{staticClass:"b-sub"},[this._v("打破系统壁垒，无缝接入外部服务，让业务流转更高效、更智能")])])])])])}]};var s=i("VU/8")({name:"HeaderBanner"},a,!1,function(t){i("hMpj")},"data-v-c7ae83ac",null).exports,n=i("Xxa5"),o=i.n(n),r=i("exGp"),l=i.n(r),c={name:"AppItem",props:{item:{type:Object,default:function(){return{}}},type:{type:String,default:"item"},showEdit:{type:Boolean,default:!1}},computed:{appItem:function(){return"add"===this.type?this.addItem:this.item}},data:function(){return{addItem:{icon:i("VUDK"),title:"应用名称",remarks:"此处为应用的简单介绍，字数建议控制在40字以内"}}},methods:{openApp:function(){this.item.url&&window.open(this.item.url,"_blank")},handleAddClick:function(){this.$emit("click")},handleEdit:function(){this.$emit("edit",this.item)},handleDelete:function(){this.$emit("delete",this.item)}}},d={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-item-wrap",class:{"add-item":"add"===t.type}},[a("div",{staticClass:"app-icon"},[a("img",{attrs:{src:t.appItem.icon,alt:""}})]),t._v(" "),a("div",{staticClass:"app-item"},["add"!==t.type&&t.showEdit?a("div",{staticClass:"action-icons"},[a("img",{staticClass:"action-icon edit-icon",attrs:{src:i("KNxP"),alt:"编辑"},on:{click:function(e){return e.stopPropagation(),t.handleEdit.apply(null,arguments)}}}),t._v(" "),a("img",{staticClass:"action-icon delete-icon",attrs:{src:i("trJg"),alt:"删除"},on:{click:function(e){return e.stopPropagation(),t.handleDelete.apply(null,arguments)}}})]):t._e(),t._v(" "),a("div",{staticClass:"app-item-content"},[a("h3",{staticClass:"item-title"},[t._v(t._s(t.appItem.title))]),t._v(" "),a("p",{staticClass:"item-desc"},[t._v(t._s(t.appItem.remarks))])]),t._v(" "),"add"===t.type?a("div",{staticClass:"app-item-btn",on:{click:t.handleAddClick}},[a("img",{staticClass:"icon",attrs:{src:i("Kri2"),alt:""}}),t._v(" "),a("span",[t._v("添加应用")])]):a("div",{staticClass:"app-item-btn",on:{click:t.openApp}},[a("span",[t._v("打开应用")]),t._v(" "),a("i",{staticClass:"el-icon-arrow-right"})])])])},staticRenderFns:[]};var p=i("VU/8")(c,d,!1,function(t){i("O2Qw")},"data-v-7cbcb806",null).exports,u=i("mvHQ"),m=i.n(u),f={name:"LogoUpload",props:{value:{type:String,default:""},disabled:{type:Boolean,default:!1},uploadUrl:{type:String,default:"/api/workorder/upload"}},data:function(){return{logoUrl:"",uploadLoading:!1}},computed:{token:function(){return this.$cookies.get("token")||this.$cookies.get("access_token")||this.$store.getters.token}},watch:{value:{handler:function(t){this.logoUrl=t},immediate:!0}},methods:{beforeUpload:function(){this.uploadLoading=!0},handleSuccess:function(t,e){if(this.uploadLoading=!1,200==t.code){var i=t.data.url;this.$emit("success",i)}}}},h={render:function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"logo-upload-wrapper"},[i("el-upload",{directives:[{name:"loading",rawName:"v-loading",value:t.uploadLoading,expression:"uploadLoading"}],staticClass:"avatar-uploader",attrs:{name:"file",data:{token:t.token},action:t.uploadUrl,"show-file-list":!1,"before-upload":t.beforeUpload,"on-success":t.handleSuccess,disabled:t.disabled,accept:"image/*"}},[t.logoUrl?i("el-image",{staticClass:"avatar",attrs:{fit:"contain",src:t.logoUrl}}):i("i",{staticClass:"el-icon-plus avatar-uploader-icon"})],1)],1)},staticRenderFns:[]};var v=i("VU/8")(f,h,!1,function(t){i("Kkxl")},"data-v-1a530b24",null).exports,g=i("SgJ7"),b={name:"AddApp",components:{LogoUpload:v},data:function(){return{visible:!1,form:{title:"",icon:"",remarks:"",url:""},rules:{title:[{required:!0,message:"请输入应用名称",trigger:"blur"}],icon:[{required:!0,message:"请上传应用图标",trigger:"blur"}],remarks:[{required:!0,message:"请输入应用描述",trigger:"blur"}],url:[{required:!0,message:"请输入应用链接",trigger:"blur"},{validator:function(t,e,i){/^https?:\/\/.*/.test(e)?i():i(new Error("请输入正确的链接"))},trigger:"blur"}]}}},computed:{title:function(){return this.form.id?"编辑应用":"添加应用"}},methods:{openDialog:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;if(this.visible=!0,t){var e=t.id,i=t.title,a=t.icon,s=t.remarks,n=t.url;this.form={id:e,title:i,icon:a,remarks:s,url:n}}else this.form={title:"",icon:"",remarks:"",url:""}},handleLogoSuccess:function(t){console.log("LOGO上传成功-----:",t),this.form.icon=t},handleSubmit:function(){var t=this;this.$refs.form.validate(function(e){if(!e)return console.log("表单验证失败"),!1;console.log("表单提交:",t.form);var i=JSON.parse(m()(t.form));Object(g.z)(i).then(function(e){if(200===e.code){var i=t.form.id?"编辑成功":"添加成功";t.$message.success(i),t.$emit("success"),t.visible=!1}})})}}},_={render:function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("el-dialog",{attrs:{title:t.title,visible:t.visible,width:"606px"},on:{"update:visible":function(e){t.visible=e}}},[i("el-form",{ref:"form",attrs:{model:t.form,rules:t.rules,"label-width":"70px","label-position":"left"}},[i("el-form-item",{attrs:{label:"LOGO",prop:"icon"}},[i("LogoUpload",{on:{success:t.handleLogoSuccess},model:{value:t.form.icon,callback:function(e){t.$set(t.form,"icon",e)},expression:"form.icon"}})],1),t._v(" "),i("el-form-item",{attrs:{label:"名称",prop:"title"}},[i("el-input",{attrs:{placeholder:"请输入应用名称",clearable:""},model:{value:t.form.title,callback:function(e){t.$set(t.form,"title",e)},expression:"form.title"}})],1),t._v(" "),i("el-form-item",{attrs:{label:"网址",prop:"url"}},[i("el-input",{attrs:{placeholder:"请输入应用网址",clearable:""},model:{value:t.form.url,callback:function(e){t.$set(t.form,"url",e)},expression:"form.url"}})],1),t._v(" "),i("el-form-item",{attrs:{label:"描述",prop:"remarks"}},[i("el-input",{attrs:{type:"textarea",placeholder:"请输入应用描述",rows:4,maxlength:40,"show-word-limit":"",resize:"none",clearable:""},model:{value:t.form.remarks,callback:function(e){t.$set(t.form,"remarks",e)},expression:"form.remarks"}})],1)],1),t._v(" "),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{on:{click:function(e){t.visible=!1}}},[t._v("取 消")]),t._v(" "),i("el-button",{attrs:{type:"primary"},on:{click:t.handleSubmit}},[t._v("确 定")])],1)],1)},staticRenderFns:[]},k={name:"AppList",components:{AppItem:p,AddApp:i("VU/8")(b,_,!1,null,null,null).exports},data:function(){return{showAdd:!1,list:[]}},mounted:function(){this.getList()},methods:{getList:function(){var t=this;return l()(o.a.mark(function e(){var i,a,s,n;return o.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Object(g.v)();case 2:i=e.sent,console.log(i,"三方应用列表"),200===i.code&&(a=i.data,s=a.list,n=a.power,t.list=s,t.showAdd=n);case 5:case"end":return e.stop()}},e,t)}))()},handleAddApp:function(){this.$refs.addAppRef.openDialog()},handleSuccess:function(){this.getList()},handleEdit:function(t){console.log("编辑应用:",t),this.$refs.addAppRef.openDialog(t)},handleDelete:function(t){var e=this;console.log("删除应用:",t),this.$confirm("确定要删除这个应用吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){Object(g.c)({id:t.id}).then(function(t){200===t.code&&(e.$message.success("删除成功"),e.getList())})}).catch(function(){})}}},w={render:function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"app-list-wrapper"},[t.showAdd?i("app-item",{attrs:{type:"add"},on:{click:t.handleAddApp}}):t._e(),t._v(" "),t._l(t.list,function(e,a){return i("AppItem",{key:a,attrs:{item:e,showEdit:t.showAdd},on:{edit:t.handleEdit,delete:t.handleDelete}})}),t._v(" "),i("AddApp",{ref:"addAppRef",on:{success:t.handleSuccess}})],2)},staticRenderFns:[]};var C={name:"dashboardApp",components:{HeaderBanner:s,AppList:i("VU/8")(k,w,!1,function(t){i("E+5q")},"data-v-cdd26f46",null).exports}},A={render:function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"app-container"},[e("HeaderBanner"),this._v(" "),e("AppList")],1)},staticRenderFns:[]};var $=i("VU/8")(C,A,!1,function(t){i("cFGC")},"data-v-4d8538de",null);e.default=$.exports},cFGC:function(t,e){},hMpj:function(t,e){}});