import request from '@/utils/request'
import requestJzt from '@/utils/requestJzt'
import requestIhr from '@/utils/requestIhr'
import request_no_message from '../utils/request_no_message'

// 获取我的操作
export function getMyActions (data) {
  return request({
    url: '/api/app/commonlyNewsUsed',
    method: 'post',
    data
  })
}

// 判断是否显示建站通和建站通商城版
export function isJzt (data) {
  return request({
    url: '/api/app/commonlyckjzt',
    method: 'post',
    data
  })
}

// 获取建站通数据
export function getJztData (data) {
  return requestJzt({
    url: '/zhydata/sel?company_unique_id=' + data,
    method: 'get'
  })
}

// 获取建站通商城版数据
export function getJztShopData (data) {
  return request({
    url: '/api/app/shopStatistics',
    method: 'post',
    data
  })
}

// 获取私域、带货电商数据
export function getSyData (data) {
  return request({
    url: '/api/app/Ecommerce',
    method: 'post',
    data
  })
}

// 获取传统电商数据
export function getCtData (data) {
  return request({
    url: '/api/app/traditionListEcommerce',
    method: 'post',
    data
  })
}

// 获取传统电商数据
export function getCtDataNew (data) {
  return request({
    url: '/api/app/traditionListEcommerceNew',
    method: 'post',
    data
  })
}

// 收藏电商数据
export function getCollect (data) {
  return request({
    url: '/api/app/addEcommerce',
    method: 'post',
    data
  })
}

// 删除电商数据
export function delCollect (data) {
  return request({
    url: '/api/app/destoryEcommerce',
    method: 'post',
    data
  })
}

// 获取考勤统计
export function getAttendance (data) {
  return requestIhr({
    url: `/baseapi/examination/my_attendance`,
    method: 'post',
    data
  })
}

// 获取日程
export function getSchedule (data) {
  return request({
    url: '/api/todo/list',
    method: 'post',
    data
  })
}

// 获取客户关键数据
export function getCustomerData (data) {
  return request({
    url: '/api/role/getCustomerData',
    method: 'post',
    data
  })
}

// 获取电商分类列表
export function getEcommerceList (data) {
  return request({
    url: '/api/app/traditionEcommerce',
    method: 'post',
    data
  })
}

// 考勤统计
export function getAttendanceStatistics (data = {}) {
  return request_no_message({
    url: '/api/role/webDataAttendance',
    method: 'post',
    data,
    baseURL: process.env.BASE_API
  })
}

// ai工具推荐列表
export function getAiToolList (data = {}) {
  return request({
    url: '/api/app/aiToolRecommend',
    method: 'post',
    data
  })
}

// 获取全域电商-绑定店铺列表 type电商类型 shop_type分类id
export function getEcommerceShopList (data = {}) {
  return request({
    url: '/api/app/listShopManages',
    method: 'post',
    data
  })
}

// 全域电商-绑定店铺 type电商类型 shop_type分类id username电商账号 password电商密码
export function bindEcommerceShop (data = {}) {
  return request({
    url: '/api/app/addShopManages',
    method: 'post',
    data
  })
}

// 全域电商-解绑店铺
export function unbindEcommerceShop (data = {}) {
  return request({
    url: '/api/app/delShopManages',
    method: 'post',
    data
  })
}
// 全域电商-店铺运营数据
export function getEcommerceShopData (data = {}) {
  return request({
    url: '/api/app/getShopManageDatas',
    method: 'post',
    data
  })
}

/**
 * 私有化部署 相关配置
 */
// 获取aigc私有化配置信息
export function getAigcPrivateConfigApi (data = {}) {
  return request({
    url: '/api/role/Aigc',
    method: 'post',
    data
  })
}

// 保存aigc私有化配置信息
export function saveAigcPrivateConfigApi (data = {}) {
  return request({
    url: '/api/role/AigcAdd',
    method: 'post',
    data
  })
}

// 获取私域电商配置详情
export function getEcommercePrivateConfigApi (data = {}) {
  return request({
    url: '/api/role/shop',
    method: 'post',
    data
  })
}

// 保存私域电商配置
export function saveEcommercePrivateConfigApi (data = {}) {
  return request({
    url: '/api/role/shopAdd',
    method: 'post',
    data
  })
}

/**
 * 获取企业列表
 * @param {*} data 
 * @returns account_mold 是否分公司 1是
 */
export function getCompanyListApi (data = {}) {
  return request({
    url: '/api/user/switchCompanyData',
    method: 'post',
    data
  })
}

/**
 * 切换企业
 * @param unique_id 企业id
 * @returns
 */
export function switchCompanyApi (data = {}) {
  return request({
    url: '/api/user/cutNew',
    method: 'post',
    data
  })
}

// 获取dashboard对应菜单数据
export function getDashboardMenusApi (data = {}) {
  return request({
    url: '/api/user/chinaRoot',
    method: 'post',
    data
  })
}

// 获取三方应用列表
export function getThirdAppListApi (data = {}) {
  return request({
    url: '/api/user/thirdPartyList',
    method: 'post',
    data
  })
}

// 添加、编辑 三方应用
export function saveThirdAppApi (data = {}) {
  return request({
    url: '/api/user/editThirdParty',
    method: 'post',
    data
  })
}

// 删除 三方应用
export function delThirdAppApi (data = {}) {
  return request({
    url: '/api/user/delThirdParty',
    method: 'post',
    data
  })
}

// 获取菜单初始化
export function getLeftMenuInitApi (data = {}) {
  return request({
    url: '/api/user/appmenu',
    method: 'post',
    data
  })
}

// 自定义菜单设置
export function setLeftMenuApi (data = {}) {
  return request({
    url: '/api/user/addMenu',
    method: 'post',
    data
  })
}
