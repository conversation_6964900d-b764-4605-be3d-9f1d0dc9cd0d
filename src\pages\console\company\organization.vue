<template>
  <div class="app-container organization">
    <div class="wrap">
      <div class="left">
        <el-card class="el-box">
          <div slot="header" class="clearfix" style="display: flex; align-items: center; justify-content: space-between">
            <div>
              <span>组织架构</span>
              <span style="color: #e6a23c; font-size: 12px">（可拖拽排序）</span>
            </div>
            <!--                        <el-button size="small" style="float: right; padding: 0;line-height: 21px;" type="text" icon="el-icon-plus" @click="departmentHandle(3)">添加顶层部门</el-button>-->
            <!-- 20250427导出组织架构按钮 -->
            <el-button v-if="orgTreeImg" size="small" style="float: right; padding: 0; line-height: 21px;" type="text" icon="el-icon-download"
              @click="exportOrganization">导出组织架构</el-button>
          </div>
          <div class="tree">
            <h5 style="font-size: 16px;">{{ getParentCompany.name }}</h5>

            <div class="tree-title">
              <!-- <p>部门</p> -->
              <el-tree ref="departTree" :highlight-current="true" node-key="id" v-loading="departmentLoading"
                :data="getDepartment" :props="{ children: 'children', label: 'name', id: 'id' }" accordion
                :default-expand-all="false" :check-on-click-node="true" :expand-on-click-node="false"
                :default-checked-keys="[current_department.id]" :default-expanded-keys="checkedNodes"
                :show-checkbox="false" @node-click="handleNodeClick" :auto-expand-parent="true"
                @check-change="handleCheckChange" @node-drop="handleDrop" draggable :allow-drop="allowDrop"
                @node-expand="handleNodeExpand" @node-collapse="handleNodeCollapse">
              </el-tree>
            </div>

            <div class="tree-title">
              <h5>分公司</h5>
              <el-tree ref="departTree" :highlight-current="true" node-key="id" v-loading="departmentLoading"
                :data="getBranch" :props="{ children: 'children', label: 'name', id: 'id' }" accordion
                :default-expand-all="false" :check-on-click-node="true" :expand-on-click-node="false"
                :default-checked-keys="[current_department.id]" :default-expanded-keys="checkedNodes"
                :show-checkbox="false" @node-click="handleNodeClick" :auto-expand-parent="true"
                @check-change="handleCheckChange" @node-drop="handleDrop" draggable :allow-drop="allowDrop"
                @node-expand="handleNodeExpand" @node-collapse="handleNodeCollapse">
              </el-tree>
            </div>
          </div>
        </el-card>
      </div>
      <div class="right">
        <el-card class="el-box">
          <div slot="header" class="clearfix" style="display: flex;justify-content: space-between;flex-wrap: wrap">
            <div>
              <span style="font-size:16px;">{{ current_department.name }}</span>
              <span class="top_bar_tag" v-if="leader.responsible_name" style="margin-left:10px;font-size:12px;">部门负责人：{{ leader.responsible_name }}</span>
              <span class="top_bar_tag" v-if="leader.leader_name" style="margin-left:10px;font-size:12px;">分管领导：{{ leader.leader_name }}</span>
            </div>
            <div>
              <el-input class="name-search" style="width: 200px;margin-right: 10px;" placeholder="请输入人员姓名"
                @keydown.enter.native="searchByName" v-model="searchName">
                <i slot="suffix" class="el-input__icon el-icon-search" @click="searchByName"></i>
              </el-input>
              <el-button size="small" style="padding: 0 3px" type="text" icon="el-icon-refresh"
                @click="getEmployeeList(0)">刷新
              </el-button>
              <!-- <el-button size="small" style="padding: 0 3px" type="text" icon="el-icon-user" @click="goToHr">员工管理
              </el-button> -->
             <!-- <el-button size="small" style="padding: 0 3px" type="text" icon="el-icon-document" @click="goToPosition">
                职位管理
              </el-button>-->
              <el-button size="small" style="padding: 0 3px" type="text" icon="el-icon-plus"
                @click="departmentHandle(1)">添加子部门
              </el-button>
              <el-button size="small" style="padding: 0 3px" type="text" icon="el-icon-edit"
                :disabled="!(current_department.id && !isTop)" @click="departmentHandle(2)">修改部门
              </el-button>
              <el-button size="small" style="padding: 0 3px" type="text" icon="el-icon-delete"
                :disabled="!(current_department.id && !isTop)" @click="departmentDelete">删除部门
              </el-button>
            </div>
          </div>
          <el-table tooltip-effect="dark" v-loading="tableLoading" :data="userData" stripe
            style="width: 100%;min-height: 300px;" @selection-change="handleSelectionChange">
            <el-table-column label="序号" type="index" width="50" align="center">
            </el-table-column>
            <el-table-column label="姓名" width="300">
              <template slot-scope="scope">
                <span style="cursor: pointer;color: #409EFF;" @click="showPersonDetail(scope.row)">{{ scope.row.name }}</span>
                <el-tag type="warning" v-if="scope.row.is_manager" size="small">管理员</el-tag>
                <el-tag type="success" v-if="scope.row.is_manager && scope.row.roles_name.length > 0" size="small">
                  {{ scope.row.roles_name.length > 0 ? scope.row.roles_name[0].name : '' }}
                  {{ scope.row.roles_name.length > 1 ? 'More+' : '' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="工号" prop="job_number" width="100"></el-table-column>
            <el-table-column label="手机号码" prop="phone">
              <template slot-scope="scope">
                <span>{{ scope.row.phone }}</span>
              </template>
            </el-table-column>
            <el-table-column label="职务">
              <template slot-scope="scope">
                <el-tag type="warning" size="small" v-if="scope.row.post === null || scope.row.post === ''">未设置
                </el-tag>
                <span v-else>{{ scope.row.post }}</span>
              </template>
            </el-table-column>
            <el-table-column label="部门" prop="department"></el-table-column>
            <el-table-column label="状态">
              <template slot-scope="scope">
                <span v-if="scope.row.status === 0">待入职</span>
                <span v-if="scope.row.status === 1">在职</span>
                <span v-if="scope.row.status === 2">办理离职</span>
              </template>
            </el-table-column>
            <el-table-column label="操作">
              <template slot-scope="scope">
                <el-button type="text" size="small" @click="handleEdit(scope.row)">权限设置</el-button>
                <el-button type="text" size="small" @click="handleEditDetail(scope.row)">编辑员工</el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination :background="true" @current-change="handleCurrentChange" @size-change="handleSizeChange"
            :current-page.sync="queryList.page" :page-sizes="[10, 15, 20, 30, 50]" :page-size.sync="queryList.perPage"
            layout="total, sizes, prev, pager, next, jumper" :total="total">
          </el-pagination>
        </el-card>
      </div>
    </div>
    <personEdit ref="edit" :visible="personDialogEditVisible" @transfer="personAddSuceess"
      @cancel="personDialogEditVisible = false;" :employee="employee" destroy-on-close />
    <el-dialog :title="departmentDialogTitle" :visible.sync="departmentDialogVisible" width="50%">
      <el-form v-if="departmentDialogVisible" :model="dialog_modal" ref="dialog" :rules="rules">
        <el-form-item label="部门名称" :label-width="formLabelWidth" prop="title">
          <el-input v-model="dialog_modal.title" autocomplete="off" style="width: 300px;"
            placeholder="请输入名称"></el-input>
        </el-form-item>
        <el-form-item label="部门负责人" :label-width="formLabelWidth">
          <treeselect :options="orgTree" :disable-branch-nodes="true" :show-count="false" placeholder="请选择部门负责人"
            style="width: 300px;" v-model="dialog_modal.responsible" :normalizer="normalizer" />
        </el-form-item>
        <el-form-item label="分管领导" :label-width="formLabelWidth">
          <treeselect :options="orgTree" :disable-branch-nodes="true" :show-count="false" placeholder="请选择分管领导"
            style="width: 300px;" v-model="dialog_modal.leader" :normalizer="normalizer" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="departmentDialogVisible = false" size="small">取 消</el-button>
        <el-button type="primary" @click="departmentSubmit" size="small">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog title="员工档案" :visible.sync="personDetailDialog" fullscreen class="archives">
      <iframe id="iframe" :src="detailUrl" frameborder="0" width="100%" style="height: 90vh"></iframe>
    </el-dialog>

    <el-dialog title="组织架构" :visible.sync="orgTreeDialogVisible" fullscreen class="orgTreeImg">
      <iframe id="iframe" :src="orgTreeImg" frameborder="0" width="100%" style="height: 90vh"></iframe>
    </el-dialog>
  </div>
</template>

<script>
/* eslint-disabled */
import Pagination from '@/components/Pagination'
import personEdit from '../components/organization-person-update'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'

import {
  departmentCreate,
  departmentUpdate,
  departmentDelete,
  departmentIndex,
  employeeRemove,
  departmentSort,
  getUserListByDepartmentId
} from '@/api/console'

import { mapMutations, mapState } from 'vuex'
import {getLeaderNew, getOrgTreeImg, getOrgTreeNew} from '../../../api/console'
import SelfSelectDialog from '../../../components/selfSelectCopy/SelectDialog.vue'
import UserSelect from './userSelect.vue'

export default {
  name: 'organization',
  components: { UserSelect, SelfSelectDialog, Pagination, personEdit, Treeselect },
  data () {
    return {
      permissionDialogEditVisible: false,
      total: 0,
      queryList: { page: 1, perPage: 10 },
      departmentData: [],
      userData: [],
      departmentLoading: false,
      tableLoading: false,
      current_department: {
        title: 'loading...'
      },
      personDialogVisible: false,
      personDialogEditVisible: false,
      personOrgDialogVisible: false,
      departmentDialogVisible: false,
      multipleSelection: [],
      departmentDialogTitle: '',
      formLabelWidth: '120px',
      dialog_modal: {},
      employee: {},
      oldEmployee: '',
      isDisabled: true,
      rules: {
        title: [
          { required: true, message: '部门名称不能为空', trigger: 'blur' }
        ]
      },
      value: null,
      org_tree_options: [],
      checkedNodes: [],
      searchName: '',
      employeesList: [],
      topId: null,
      checkedId: null,
      personDetailDialog: false,
      detailUrl: '',
      leader: {
        responsible_name: undefined,
        leader_name: undefined
      },
      normalizer (node) {
        return {
          id: node['i-id'],
          label: node.name,
          children: node.child
        }
      },
      permkey: 0,
      orgTree: [],
      orgTreeResponsive: [],
      orgTreeLeader: [],
      checkOptions: [],
      selectedData: [],
      multiple: false,
      initDataResponsible: [],
      initDataLeader: [],
      isTop: true,
      orgTreeImg: '',
      orgTreeDialogVisible: false
    }
  },
  computed: {
    ...mapState(['extendDepartmentTrees']),
    getParentCompany () {
      return this.departmentData[0] || {}
    },
    getBranch () {
      if (this.getParentCompany && this.getParentCompany.children && this.getParentCompany.children.length) {
        let list = this.getParentCompany.children.filter(item => item.type === 'c')
        console.log(list, 'list')
        return list
      } else {
        return []
      }
    },
    getDepartment () {
      if (this.getParentCompany && this.getParentCompany.children && this.getParentCompany.children.length) {
        return this.getParentCompany.children.filter(item => item.type === 'o')
      } else {
        return []
      }
    }
  },
  created () {
    // this.getOrgTreeNew()
    this.getOrgTreeData()
    this.getEmployeeList()
    this.getOrgTreeImg()
  },
  mounted () {
    this.employeesList = [
      {
        id: 1,
        name: '张三'
      },
      {
        id: 2,
        name: '李四'
      }
    ]
  },
  watch: {
    current_department: {
      handler (newName, oldName) {
        console.log(newName.name)
        if (newName) {
          if (newName.id && this.org_tree_options[0]) {
            this.isTop = newName.id === this.org_tree_options[0].id
          }
        }
        if (newName !== oldName) {
          if (!newName.title || newName.id) {
            this.getLeader()
            this.getEmployeeList()
          }
        }
      },
      deep: true,
      immediate: true
    },
    personDetailDialog: {
      handler (newValue, oldValue) {
        if (!newValue) {
          this.detailUrl = ''
        } else {
          if (this.detailUrl) {
            setTimeout(() => {
              this.iframeLoad()
            }, 1000)
          }
        }
      }
    }
  },
  methods: {
    iframeLoad () {
      const that = this
      const iframe = document.getElementById('iframe')
      console.log(iframe, 'iframe')
      iframe.onload = () => {
        console.log('iframe加载完成')
        that.loading = false
        window.addEventListener('message', (ev) => {
          let origin = ['http://ihr.china9.cn', 'https://ihr.china9.cn']
          if (origin.includes(ev.origin)) {
            if (ev.data === 'ihr关闭iframe') {
              console.log(ev)
              this.personDetailDialog = false
            }
          }
        })
      }
    },
    ...mapMutations(['setExtendDepartmentTrees']),
    // 刚进入获取组织架构
    getDepartmentList (pid) {
      this.departmentLoading = true
      departmentIndex({ type: '' }).then(res => {
        if (res.code === 200) {
          if (res.data[0]) {
            this.org_tree_options = res.data
            if (!this.current_department.id) {
              this.current_department = res.data[0]
            }
            let { id } = this.org_tree_options[0] || res.data[0]
            this.departmentData = res.data
            if (this.extendDepartmentTrees) {
              if (this.extendDepartmentTrees.length) {
                this.extendDepartmentTrees.forEach(v => {
                  if (v) {
                    if (JSON.stringify(this.departmentData).includes(v.toString())) {
                      this.checkedNodes.push(v)
                    } else {
                      this.checkedNodes.push(pid)
                    }
                  }
                })
              } else {
                this.checkedNodes = [id]
                this.setExtendDepartmentTrees([id])
              }
            } else {
              this.checkedNodes = [id]
              this.setExtendDepartmentTrees([])
            }
          }
        }
      }).finally(() => {
        this.departmentLoading = false
      })
    },
    handleCurrentChange (val) {
      this.queryList.page = val
      this.getLeader()
      this.getEmployeeList()
    },
    handleSizeChange (val) {
      this.queryList.perPage = val
      this.getLeader()
      this.getEmployeeList()
    },
    // 分管领导
    getLeader (pid = 0) {
      getLeaderNew({ id: this.current_department.id !== undefined ? this.current_department.id : pid }).then(res => {
        if (res.code === 200) {
          this.leader = res.data
          this.initDataResponsible = [res.data.responsible]
          this.initDataLeader = [res.data.leader]
          this.current_department = Object.assign(this.current_department, res.data)
        }
      })
    },
    // 人员列表
    getEmployeeList (pid = this.current_department.id, name) {
      this.tableLoading = true
      let data = {
        page: this.queryList.page,
        limit: this.queryList.perPage,
        status: 1 // 在职/离职/待入职
      }
      if (this.current_department.id || pid) {
        data['department_id'] = this.current_department.id !== undefined ? this.current_department.id : pid
      }
      data.name = this.searchName
      getUserListByDepartmentId(data).then(res => {
        this.tableLoading = false
        if (res.code === 200) {
          this.userData = res.data.data
          this.total = res.data.total
        }
      })
    },
    goToHr () {
      window.location.href = 'http://ihr.china9.cn/human/main/index?v=hr#/department/members'
    },
    goToPosition () {
      this.$router.push('/company/position')
    },
    employeeRemove (row) {
      this.$confirm('确定为该员工办理离职手续嘛, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        var phones = [
          row.phone
        ]
        employeeRemove({ 'phones': phones }).then(res => {
          if (res.code === 200) {
            this.$message({
              type: 'success',
              message: '操作成功!'
            })
            this.getEmployeeList()
          } else {
            this.$message({
              type: 'error',
              message: res.message
            })
          }
        })
      })
    },
    handleCheckChange (data, checked, node) {
      if (checked === true) {
        this.checkedId = data.id
        this.$refs.departTree.setCheckedKeys([data.id])
      } else {
        if (this.checkedId === data.id) {
          this.$refs.departTree.setCheckedKeys([data.id])
        }
      }
    },
    handleNodeExpand (data, node, ev) {
      this.setExtendDepartmentTrees([data.id])
      this.checkedNodes = this.extendDepartmentTrees
      this.$once('hook:beforeDestory', () => {
        this.setExtendDepartmentTrees([])
      })
    },
    handleNodeCollapse (data, node, ev) {
      let arr = []
      arr = this.removeArrEle(data.id, this.extendDepartmentTrees)
      if (arr.length === 0) {
        if ('pid' in data) {
          arr.push(data.pid)
        }
      }
      this.setExtendDepartmentTrees(arr)
      this.checkedNodes = this.extendDepartmentTrees
      this.$once('hook:beforeDestory', () => {
        this.setExtendDepartmentTrees([])
      })
    },
    removeArrEle (val, array) {
      var index = array.indexOf(val)
      if (index > -1) {
        array.splice(index, 1)
      }
      return array
    },
    handleNodeClick (res, node, current) {
      // 判断如果是最顶层，则不收缩
      if (res.init) {
        node.expanded = true
      }
      // 如果点击的同一个，则不处理
      if (res.id === this.current_department.id) {
        return
      }
      this.multipleSelection = []
      this.current_department = JSON.parse(JSON.stringify(res))

      this.setExtendDepartmentTrees([node.data.id])
      this.checkedNodes = this.extendDepartmentTrees
      this.$once('hook:beforeDestory', () => {
        this.setExtendDepartmentTrees([])
      })
    },
    allowDrop (draggingNode, dropNode, type) {
      // 拖拽允许控制器
      // 如果拖拽的是默认部门 或者 参照的对象是默认部门，不允许拖拽
      return !(draggingNode.data.init || dropNode.data.init)
    },
    /**
     * 拖拽成功完成时触发的事件
     * @draggingNode：被拖拽节点对应的 Node
     * @dropNode：结束拖拽时最后进入的节点
     * @dropType：被拖拽节点的放置位置
     * @ev：事件对象
     */
    handleDrop (draggingNode, dropNode, dropType, ev) {
      let data = {
        id: draggingNode.data.id,
        pid: null,
        target_id: null
      }
      switch (dropType) {
        case 'before':
          if (dropNode.data.id === this.topId) {
            this.$message.warning('只能有一个顶级部门')
            return this.getDepartmentList()
          }
          if (dropNode.data.type === 'c' || draggingNode.data.type === 'c') {
            return this.$message.error('暂不支持公司排序')
          }
          data.pid = dropNode.data.pid
          data.target_id = dropNode.data.id
          break
        case 'after':
          if (dropNode.data.type === 'c' || draggingNode.data.type === 'c') {
            this.$message.error('暂不支持公司排序')
            return this.getDepartmentList()
          }
          data.pid = dropNode.data.pid
          data.target_id = dropNode.data.id
          break
        case 'inner':
          if (dropNode.data.type === 'c' || draggingNode.data.type === 'c') {
            this.$message.error('不可跨公司移动')
            return this.getDepartmentList()
          }
          data.pid = dropNode.data.id
          if (dropNode.data.children.length > 1) {
            data.target_id = dropNode.data.children[dropNode.data.children.length - 2].id
          } else {
            data.target_id = dropNode.data.id
          }
          break
      }

      this.setExtendDepartmentTrees([draggingNode.data.pid])
      this.checkedNodes = this.extendDepartmentTrees
      this.$once('hook:beforeDestory', () => {
        this.setExtendDepartmentTrees([])
      })
      departmentSort(data).then(res => {
        if (res.code === 200) {
          this.getDepartmentList()
        }
      })
    },
    // 点击编辑
    handleEdit (employee) {
      employee.yunjingliDefaultCheck = []
      if (employee.yunJinLi && employee.yunJinLi.length) {
        employee.yunjingliDefaultCheck = employee.yunJinLi.filter(value => value.auth).map(value => value.unique_id)
      }
      this.employee = employee
      this.personDialogEditVisible = true
      this.$refs.edit.getRoles()
      this.$refs.edit.getGroup()
      this.$forceUpdate()
    },
    personDialog () {
      this.personDialogVisible = true
      this.$forceUpdate()
    },
    personOrgDialog () {
      this.personOrgDialogVisible = true
      this.$forceUpdate()
    },
    personAddSuceess () {
      this.getLeader()
      this.personCancel()
      this.personEditCancel()
      this.getEmployeeList()
    },
    personCancel () {
      this.personDialogVisible = false
      this.personOrgDialogVisible = false
    },
    personEditCancel () {
      this.personDialogEditVisible = false
      this.employee = {}
    },
    handleSelectionChange (val) {
      this.multipleSelection = val
    },
    departmentHandle (type) {
      switch (type) {
        case 1:
          this.dialog_modal = {
            title: '',
            pid: this.current_department.id !== undefined ? this.current_department.id : 0
          }
          this.departmentDialogTitle = '新建部门'
          break
        case 2:
          this.dialog_modal = {
            id: this.current_department.id,
            title: this.current_department.name,
            pid: this.current_department.type === 'c' ? 0 : this.current_department.pid,
            order_id: this.current_department.order_id,
            responsible: (this.current_department.responsible === 0 ? null : this.current_department.responsible) || undefined,
            leader: (this.current_department.leader === 0 ? null : this.current_department.leader) || undefined
          }
          this.departmentDialogTitle = '修改部门'
          break
        case 3:
          this.dialog_modal = {
            title: '',
            pid: 0
          }
          this.departmentDialogTitle = '新建顶层部门'
          break
        default:
          break
      }
      this.getOrgTreeNew()
      this.personDialogVisible = false
      this.personOrgDialogVisible = false
      this.departmentDialogVisible = true
    },
    departmentSubmit () {
      this.$refs['dialog'].validate((valid) => {
        if (!valid) {
          return false
        }
        switch (this.departmentDialogTitle) {
          case '新建部门':
            departmentCreate(this.dialog_modal).then(res => {
              if (res.code === 200) {
                this.departmentSuccess('新建成功')
              }
            })
            break
          case '修改部门':
            // this.dialog_modal.responsible = this.initDataResponsible[0]
            // this.dialog_modal.leader = this.initDataLeader[0]
            departmentUpdate(this.dialog_modal).then(res => {
              if (res.code === 200) {
                this.current_department.name = this.dialog_modal.title
                this.current_department.responsible = this.dialog_modal.responsible
                this.current_department.leader = this.dialog_modal.leader
                this.departmentSuccess('修改完成')
                this.getOrgTreeData(this.dialog_modal.id)
                this.getLeader()
              }
            })
            break
          case '新建顶层部门':
            departmentCreate(this.dialog_modal).then(res => {
              if (res.code === 200) {
                this.departmentSuccess('新建成功')
              }
            })
            break
          default:
            break
        }
        // this.getLeader()
      })
    },
    expendDepartmentData (data, id) {
      data.forEach((v) => {
        if (v.children) {
          if (v.children.length) {
            this.expendDepartmentData(v.children, id)
          } else {
            if (v.id === id) {
              this.current_department.responsible_name = v.responsible_name
              this.current_department.leader_name = v.leader_name
            }
          }
        } else {
          if (v.id === id) {
            this.current_department.responsible_name = v.responsible_name
            this.current_department.leader_name = v.leader_name
          }
        }
      })
    },
    departmentDelete () {
      this.$confirm('删除部门, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        departmentDelete({ id: this.current_department.id }).then(res => {
          if (res.code === 200) {
            this.departmentSuccess('删除成功', this.current_department.pid)
          }
        })
      })
    },
    departmentSuccess (mes, pid) {
      this.$message.success(mes)
      this.getDepartmentList(pid)
      this.departmentDialogVisible = false
    },
    // 获取组织架构
    getOrgTreeData (editId) {
      this.departmentLoading = true
      if (editId) {
        this.expendDepartmentData(this.departmentData, this.dialog_modal.id)
      } else {
        this.getDepartmentList()
      }
    },
    formatTreeData (data) {
      var list = []
      for (let i = 0; i < data.length; i++) {
        var item = []
        item['id'] = data[i]['id']
        item['label'] = data[i]['name']
        item['type'] = data[i]['type']
        if (data[i]['children'].length > 0) {
          item['children'] = this.formatTreeData(data[i]['children'])
        } else if (data[i]['type'] === 'org') {
          item['isDisabled'] = true
        }
        list.push(item)
      }
      return list
    },
    searchByName () {
      this.queryList.page = 1
      this.getEmployeeList(0, this.searchName)
    },
    showPersonDetail (row) {
      this.detailUrl = 'https://ihr.china9.cn/human/department/member_show_new/user_unique_id/' + row.user_unique_id
      this.personDetailDialog = true
    },
    handleEditDetail (row) {
      this.detailUrl = 'https://ihr.china9.cn/human/department/editMember/user_unique_id/' + row.user_unique_id
      this.personDetailDialog = true
    },
    parentFnResponsible (value) {
      this.initDataResponsible = value
    },
    parentFnLeader (value) {
      this.initDataLeader = value
    },
    changeCheckItemsResponsible (data) {
      this.flatten(this.orgTreeResponsive, data)
    },
    changeCheckItemsLeader (data) {
      this.flatten(this.orgTreeLeader, data)
    },
    // 展平数组
    flatten (arr, data) {
      arr.forEach(value => {
        if (value.child && value.child.length) {
          this.flatten(value.child, data)
        } else {
          if (data.length) {
            data.forEach(v => {
              if (value['i-id'] === v['i-id']) {
                this.$set(value, 'checked', v.checked)
              } else {
                this.$set(value, 'checked', false)
              }
            })
          } else {
            this.$set(value, 'checked', false)
          }
        }
      })
    },
    //   修改部门获取人员组织架构
    getOrgTreeNew () {
      getOrgTreeNew({ type: 1, unique_id: this.current_department.company_unique_id }).then(res => {
        if (res.code === 200) {
          this.orgTreeResponsive = JSON.parse(JSON.stringify(res.data))
          this.orgTreeLeader = JSON.parse(JSON.stringify(res.data))
          this.orgTree = res.data
          this.$set(this.dialog_modal, 'company_id', res.data.length ? res.data[0].company_id : '')
        }
      })
    },
    async getOrgTreeImg () {
      try {
        const res = await getOrgTreeImg()
        if (res.code === 200 && res.data) {
          this.orgTreeImg = res.data.url
        }
      } catch (e) {
        console.log(e)
      }
    },
    exportOrganization () {
      if(this.orgTreeImg) {
        this.orgTreeDialogVisible = true
      }
    }
  }
}
</script>

<style scoped>
.header {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-top: 10px;
}

.organization .tree {
  min-height: 300px;
}

.clearfix {
  min-height: 20px;
}

>>>.el-tree {
  width: 100%;
  overflow: auto;
}

>>>.el-tree>.el-tree-node {
  display: inline-block;
  min-width: 100%;
}
</style>
<style>
.vue-treeselect__placeholder,
.vue-treeselect__single-value {
  line-height: 40px !important;
  padding-left: 10px;
  padding-right: 10px;
}

.vue-treeselect--searchable .vue-treeselect__input-container {
  padding-left: 10px;
  padding-right: 10px;
}

.vue-treeselect__input {
  line-height: 40px !important;
  height: 40px !important;
}

.top_bar_tag {
  background-color: #efefef;
  border-color: #efefee;
  border-radius: 5px;
  color: #888;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  height: 24px;
  padding: 5px 8px;
  line-height: 22px;
}
</style>
<style lang="scss">
.custom-tree-node {
  display: flex;
  flex-direction: row;
  align-items: center;

  p {
    /*width: 100px;*/
    flex: 1;
    width: 100%
  }

  i {
    height: 15px;
  }
}
</style>
<style scoped lang="scss">
>>>.el-card__header {
  box-sizing: content-box;
  line-height: 34px;
}

>>>.el-input--suffix .el-input__inner {
  height: 34px;
}

>>>.el-input__suffix .el-input__icon {
  line-height: 34px;
}

>>>.el-pagination {
  text-align: center !important;
  margin-top: 20px;
}

>>>.name-search {
  input {
    font-size: 12px;

    &::placeholder {
      font-size: 12px;
    }
  }

  .el-input__suffix {
    background-color: #409EFF;
    height: calc(100% - 2px);
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    margin: auto;
    border-radius: 0 4px 4px 0;
    width: 32px;
    cursor: pointer;
    color: #fff;
    transform: translateY(1px);
  }
}

.organization {
  >>>.el-dialog__wrapper {
    z-index: 999999999 !important;

    .el-dialog__body {
      padding-top: 0 !important;
    }
  }
}

.user-select {
  >>>.btn-show {
    .cur-lists ul li {
      margin-top: 0;
    }

    .search-box {

      .search input,
      button {
        margin-top: 0;
      }
    }
  }
}

.tree-title {
  margin-left: 10px;
  margin-top: 10px;

  p {
    margin-bottom: 5px;
  }
}

.wrap{
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  gap: 20px;
  .left{
    width: 370px;
    flex-shrink: 0;
  }
  .right {
    flex: 1;
    width: calc(100% - 370px - 20px);
  }
  >>>.el-card {
    height: 100%;
    overflow: auto;
  }
}
</style>
