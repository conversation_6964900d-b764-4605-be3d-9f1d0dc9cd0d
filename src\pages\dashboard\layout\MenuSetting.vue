<template>
  <el-dialog
    title="自定义菜单"
    :visible.sync="dialogVisible"
    width="480px"
    :close-on-click-modal="false"
    :append-to-body="true"
    custom-class="menu-setting-dialog"
  >
    <div class="menu-setting-content">
      <!-- 表头 -->
      <div class="table-header">
        <div class="header-item menu-col">菜单</div>
        <div class="header-item display-col">是否显示</div>
        <div class="header-item default-col">设为首页</div>
      </div>

      <!-- 菜单列表 -->
      <draggable
        v-model="menuList"
        :options="dragOptions"
        @start="onDragStart"
        @end="onDragEnd"
        class="menu-list"
      >
        <div
          v-for="(item, index) in menuList"
          :key="item.id"
          class="menu-item"
        >
          <div class="menu-col">
            <div class="drag-handle">
              <img src="@/assets/image/icon/drag.png" alt="">
            </div>
            <div class="menu-info">
              <img v-if="item.icon" :src="item.icon" alt="" class="menu-icon">
              <span class="menu-name">{{ item.statistic }}</span>
            </div>
          </div>

          <div class="display-col">
            <el-switch
              v-model="item.hidden"
              :active-value="0"
              :inactive-value="1"
              active-color="#4D80FF"
              inactive-color="#DCDFE6"
              @change="handleSwitchChange(item)"
            />
          </div>

          <div class="default-col">
            <el-switch
              v-if="item.power"
              v-model="item.index"
              :active-value="1"
              :inactive-value="0"
              :disabled="item.hidden == 1"
              active-color="#4D80FF"
              inactive-color="#DCDFE6"
              @change="handleDefaultChange(item)"
            />
          </div>
        </div>
      </draggable>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleConfirm">完成</el-button>
    </div>
  </el-dialog>
</template>

<script>
import draggable from 'vuedraggable'
import { getLeftMenuInitApi, setLeftMenuApi } from '@/api/consoleNew'

export default {
  name: 'MenuSetting',
  components: {
    draggable
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dragOptions: {
        handle: '.drag-handle',
        animation: 200,
        ghostClass: 'ghost-item',
        chosenClass: 'chosen-item'
      },
      loading: false,
      menuList: [
        // {
        //   id: 1,
        //   name: '控制台',
        //   icon: require('@/assets/image/dashboard/layout/home.png'),
        //   enabled: true,
        //   isDefault: true
        // },
        // {
        //   id: 2,
        //   name: 'AIGC',
        //   icon: require('@/assets/image/dashboard/layout/aigc.png'),
        //   enabled: true,
        //   isDefault: false
        // },
        // {
        //   id: 3,
        //   name: '建站通',
        //   icon: require('@/assets/image/dashboard/layout/jzt.png'),
        //   enabled: true,
        //   isDefault: false
        // },
        // {
        //   id: 4,
        //   name: '电商通',
        //   icon: require('@/assets/image/dashboard/layout/ecommerce.png'),
        //   enabled: true,
        //   isDefault: false
        // },
        // {
        //   id: 5,
        //   name: '销售通',
        //   icon: require('@/assets/image/dashboard/layout/sale.png'),
        //   enabled: true,
        //   isDefault: false
        // },
        // {
        //   id: 6,
        //   name: '易管通',
        //   icon: require('@/assets/image/dashboard/layout/ygt.png'),
        //   enabled: true,
        //   isDefault: false
        // },
        // {
        //   id: 7,
        //   name: '三方应用',
        //   icon: require('@/assets/image/dashboard/layout/app.png'),
        //   enabled: true,
        //   isDefault: false
        // },
        // {
        //   id: 8,
        //   name: '系统设置',
        //   icon: require('@/assets/image/dashboard/layout/settings.png'),
        //   enabled: false,
        //   isDefault: false
        // }
      ]
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  created() {
    this.getMenuSetting()
  },
  methods: {
    // 获取菜单配置
    getMenuSetting() {
      this.loading = true
      getLeftMenuInitApi().then(res => {
        if (res.code === 200) {
          console.log(res.data, '菜单配置')
          let list = JSON.parse(JSON.stringify(res.data))
          list.forEach(item => {
            item.icon = require(`@/assets/image${item.icon}.png`)
          })
          this.menuList = list
        }
      }).catch((error) => {
        console.log(error, '获取菜单配置失败')
      }).finally(() => {
        this.loading = false
      })
    },
    onDragStart() {
      console.log('开始拖拽')
    },
    onDragEnd() {
      console.log('拖拽结束')
      // 更新排序ID
      this.updateSortIds()
    },
    // 更新排序ID
    updateSortIds() {
      this.menuList.forEach((item, index) => {
        item.sort_id = index
      })
      console.log('更新排序:', this.menuList.map(item => ({ id: item.id, name: item.statistic, sort_id: item.sort_id })))
    },
    // 处理显示开关变化
    handleSwitchChange(item) {
      // hidden: 0显示, 1隐藏
      if (item.hidden === 1 && item.index === 1) {
        // 如果隐藏的是当前默认首页，需要重新设置默认首页
        const enabledMenus = this.menuList.filter(menu => menu.hidden === 0 && menu.id !== item.id)
        if (enabledMenus.length > 0) {
          // 将第一个显示的菜单设为默认首页
          item.index = 0
          enabledMenus[0].index = 1
          this.$message.info(`已将"${enabledMenus[0].statistic}"设为默认首页`)
        } else {
          // 如果没有其他显示的菜单，不允许隐藏
          item.hidden = 0
          this.$message.warning('至少需要保留一个显示的菜单作为默认首页')
          return
        }
      } else if (item.hidden === 1) {
        // 如果隐藏的不是默认首页，直接隐藏并取消默认设置
        item.index = 0
      }
      console.log('菜单显示开关变化:', item.statistic, item.hidden === 0 ? '显示' : '隐藏')
    },
    // 处理默认首页开关变化
    handleDefaultChange(item) {
      // index: 1是默认首页, 0不是默认首页
      if (item.index === 1) {
        // 如果设置为默认首页，取消其他菜单的默认设置
        this.menuList.forEach(menu => {
          if (menu.id !== item.id) {
            menu.index = 0
          }
        })
        console.log('设置默认首页:', item.statistic)
      } else {
        // 如果取消默认首页，检查是否还有其他默认首页
        const hasDefault = this.menuList.some(menu => menu.index === 1 && menu.id !== item.id)
        if (!hasDefault) {
          // 如果没有其他默认首页，不允许取消
          item.index = 1
          this.$message.warning('必须设置一个默认首页')
          return
        }
      }
      console.log('默认首页开关变化:', item.statistic, item.index === 1 ? '是默认首页' : '不是默认首页')
    },
    handleCancel() {
      this.dialogVisible = false
    },
    handleConfirm() {
      // 验证是否有默认首页
      const defaultHome = this.menuList.find(menu => menu.index === 1 && menu.hidden === 0)
      if (!defaultHome) {
        this.$message.error('请设置一个默认首页')
        return
      }

      // 保存菜单设置
      const settings = {
        menuList: this.menuList.map(item => ({
          id: item.id,
          sort_id: item.sort_id,
          hidden: item.hidden,
          index: item.index
        }))
      }
      console.log('保存菜单设置:', settings)
      this.$message.success('菜单设置已保存')
      this.dialogVisible = false
    }
  }
}
</script>

<style scoped lang="scss">
.menu-setting-content {
  margin-top: -20px;
  margin-bottom: -20px;
  .table-header {
    display: flex;
    align-items: center;
    padding: 8px 10px;
    background-color: #F2F6F9;
    font-weight: 500;
    color: #999999;
    font-size: 14px;

    .header-item {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .menu-col {
      flex: 1;
      justify-content: flex-start;
      padding-left: 32px; // 为拖拽图标留出空间
    }

    .display-col {
      width: 100px;
    }

    .default-col {
      width: 100px;
    }
  }

  .menu-list {
    .menu-item {
      display: flex;
      align-items: center;
      padding: 14px 10px;
      cursor: move;
      transition: all 0.3s ease;

      &:hover {
        background-color: #F8F9FA;
      }

      &:last-child {
        border-bottom: none;
      }

      .menu-col {
        flex: 1;
        display: flex;
        align-items: center;

        .drag-handle {
          width: 20px;
          height: 20px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 12px;
          color: #C0C4CC;
          cursor: grab;

          &:active {
            cursor: grabbing;
          }

          img {
            width: 16px;
            height: 16px;
          }
        }

        .menu-info {
          display: flex;
          align-items: center;

          .menu-icon {
            width: 20px;
            height: 20px;
            margin-right: 14px;
          }

          .menu-name {
            font-size: 16px;
            color: #333333;
          }
        }
      }

      .display-col {
        width: 100px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .default-col {
        width: 100px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    .ghost-item {
      opacity: 0.5;
      background-color: #F0F9FF;
    }

    .chosen-item {
      background-color: #F0F9FF;
    }
  }
}

// 全局样式覆盖
.menu-setting-dialog {
  border-radius: 10px;
  .el-dialog__header {
    padding: 16px 20px;
    border-bottom: 1px solid #F0F0F0;

    .el-dialog__title {
      font-size: 16px;
      font-weight: 500;
      color: #333333;
    }
  }

  .el-dialog__body {
    padding: 20px;
  }

  .dialog-footer {
    padding: 16px 0px 0;
    border-top: 1px solid #F0F0F0;
    text-align: right;

    .el-button {
      margin-left: 8px;
    }

    .el-button--primary {
      background-color: #4D80FF;
      border-color: #4D80FF;
    }
  }
}
</style>