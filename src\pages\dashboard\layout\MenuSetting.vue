<template>
  <el-dialog
    title="自定义菜单"
    :visible.sync="dialogVisible"
    width="387px"
    :close-on-click-modal="false"
    :append-to-body="true"
    custom-class="menu-setting-dialog"
  >
    <div class="menu-setting-content">
      <draggable
        v-model="menuList"
        :options="dragOptions"
        @start="onDragStart"
        @end="onDragEnd"
        class="menu-list"
      >
        <div
          v-for="(item, index) in menuList"
          :key="item.id"
          class="menu-item"
        >
          <div class="drag-handle">
            <img src="@/assets/image/icon/drag.png" alt="">
          </div>
          <div class="menu-info">
            <img :src="item.icon" alt="" class="menu-icon">
            <span class="menu-name">{{ item.name }}</span>
          </div>
          <div class="menu-switch">
            <el-switch
              v-model="item.enabled"
              active-color="#4D80FF"
              inactive-color="#DCDFE6"
            />
          </div>
        </div>
      </draggable>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleConfirm" size="small">完成</el-button>
    </div>
  </el-dialog>
</template>

<script>
import draggable from 'vuedraggable'

export default {
  name: 'MenuSetting',
  components: {
    draggable
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dragOptions: {
        handle: '.drag-handle',
        animation: 200,
        ghostClass: 'ghost-item',
        chosenClass: 'chosen-item'
      },
      menuList: [
        {
          id: 1,
          name: '控制台',
          icon: require('@/assets/image/dashboard/layout/home.png'),
          enabled: true
        },
        {
          id: 2,
          name: 'AIGC',
          icon: require('@/assets/image/dashboard/layout/aigc.png'),
          enabled: true
        },
        {
          id: 3,
          name: '建站通',
          icon: require('@/assets/image/dashboard/layout/jzt.png'),
          enabled: true
        },
        {
          id: 4,
          name: '电商通',
          icon: require('@/assets/image/dashboard/layout/ecommerce.png'),
          enabled: true
        },
        {
          id: 5,
          name: '销售通',
          icon: require('@/assets/image/dashboard/layout/sale.png'),
          enabled: true
        },
        {
          id: 6,
          name: '易管通',
          icon: require('@/assets/image/dashboard/layout/ygt.png'),
          enabled: true
        },
        {
          id: 7,
          name: '三方应用',
          icon: require('@/assets/image/dashboard/layout/app.png'),
          enabled: true
        },
        {
          id: 8,
          name: '系统设置',
          icon: require('@/assets/image/dashboard/layout/settings.png'),
          enabled: false
        }
      ]
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  methods: {
    onDragStart() {
      console.log('开始拖拽')
    },
    onDragEnd() {
      console.log('拖拽结束')
    },
    handleCancel() {
      this.dialogVisible = false
    },
    handleConfirm() {
      // 保存菜单设置
      console.log('保存菜单设置:', this.menuList)
      this.$message.success('菜单设置已保存')
      this.dialogVisible = false
    }
  }
}
</script>

<style scoped lang="scss">
.menu-setting-content {
  .menu-list {
    .menu-item {
      display: flex;
      align-items: center;
      padding: 14px 0;
      cursor: move;
      transition: all 0.3s ease;

      &:hover {
        background-color: #F8F9FA;
      }

      &:last-child {
        border-bottom: none;
      }

      .drag-handle {
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        color: #C0C4CC;
        cursor: grab;

        &:active {
          cursor: grabbing;
        }

        i {
          font-size: 16px;
        }
      }

      .menu-info {
        flex: 1;
        display: flex;
        align-items: center;

        .menu-icon {
          width: 20px;
          height: 20px;
          margin-right: 8px;
        }

        .menu-name {
          font-size: 14px;
          color: #333333;
        }
      }

      .menu-switch {
        margin-left: 12px;
      }
    }

    .ghost-item {
      opacity: 0.5;
      background-color: #F0F9FF;
    }

    .chosen-item {
      background-color: #F0F9FF;
    }
  }
}

// 全局样式覆盖
:global(.menu-setting-dialog) {
  .el-dialog__header {
    padding: 16px 20px;
    border-bottom: 1px solid #F0F0F0;

    .el-dialog__title {
      font-size: 16px;
      font-weight: 500;
      color: #333333;
    }
  }

  .el-dialog__body {
    padding: 20px;
  }

  .dialog-footer {
    padding: 16px 0px 0;
    border-top: 1px solid #F0F0F0;
    text-align: right;

    .el-button {
      margin-left: 8px;
    }

    .el-button--primary {
      background-color: #4D80FF;
      border-color: #4D80FF;
    }
  }
}
</style>