webpackJsonp([1],{TU7B:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n("Dd8w"),a=n.n(r),s=n("NYxO"),i={name:"IframeView",data:function(){return{src:"",loading:!0}},computed:a()({},Object(s.e)(["user","zhUserRole","dashboardMenus","leftMenuLoading"]),{userInfo:function(){return console.log("userInfo",this.user),this.user&&this.user.memberInfo},companyInfo:function(){return this.user&&this.user.companyInfo},currentMenu:function(){var e=this,t=this.dashboardMenus.find(function(t){return t.path==e.$route.path});return t||this.dashboardMenus.forEach(function(n){n.children&&n.children.length&&n.children.forEach(function(n){n.path==e.$route.path&&(t=n)})}),console.log("currentMenu",t),t}}),created:function(){this.setSrc()},watch:{$route:function(){this.setSrc()},leftMenuLoading:function(e,t){console.log("leftMenuLoading",e,t),this.setSrc()}},methods:{setSrc:function(e){var t=this,n=t.$route.meta;t.src="";var r=this.currentMenu&&this.currentMenu.component||n.iframeUrl;r&&(r.includes("?")?r+="&v="+(new Date).getTime():r+="?v="+(new Date).getTime(),n.params&&n.params.length&&n.params.forEach(function(e){r+="&"+e.key+"="+t.handleParams(e.value)}),t.loading=!0,t.src=r)},handleParams:function(e){switch(e){case"user_uid":return this.userInfo.uid}},handleIframeLoad:function(){var e=this.$refs.iframeRef;console.log("iframe加载完成"),this.loading=!1;try{var t=e.contentDocument;console.log("iframe内容:",t)}catch(e){console.warn("跨域访问被阻止",e)}}}},o={render:function(){var e=this.$createElement,t=this._self._c||e;return t("div",{staticClass:"iframe-container"},[this.loading?t("div",{staticClass:"loading"},[t("img",{staticClass:"loading-icon",attrs:{src:n("QvV8"),alt:""}}),this._v(" "),t("span",{staticClass:"text"},[this._v("加载中...")])]):this._e(),this._v(" "),t("iframe",{ref:"iframeRef",staticClass:"iframe-wrap",attrs:{src:this.src},on:{load:this.handleIframeLoad}})])},staticRenderFns:[]};var c=n("VU/8")(i,o,!1,function(e){n("kqE4")},"data-v-3d5f6598",null);t.default=c.exports},kqE4:function(e,t){}});