webpackJsonp([95],{"0CIe":function(a,t,e){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=e("pI5c"),n={name:"sureOrder",data:function(){return{packageLoading:!1,tableLoading:!1,uploadLoading:!1,tableData:[],amount:0,cheapPrice:0,formData:{app_id:"",package_id:"",activity_id:"",order_type:1,month:0,source:"pc",remark:""},year:1,packages:[]}},created:function(){var a=this.$route.query;a&&a.app_id&&(this.formData.app_id=a.app_id,this.formData.activity_id=a.activity_id?a.activity_id:"",this.getAppMy(a.app_id),this.getPackages()),this.getAppAmount()},methods:{commit:function(){var a=this;this.uploadLoading=!0,this.formData.month=12*this.year,Object(i._14)(this.formData).then(function(t){a.uploadLoading=!1,a.$router.push({path:"/pay",query:{no:t.data.order_no}})}).catch(function(){a.uploadLoading=!1})},getAppMy:function(a){Object(i.f)({}).then(function(t){t.data.forEach(function(t){t.id===parseInt(a)&&(window.location.href=t.entrance_url)})})},packageChange:function(a){this.formData.package_id=a,this.getAppInfo(),this.getAppAmount()},getAppInfo:function(){var a=this;Object(i.d)({apps_info:[this.formData]}).then(function(t){a.tableData=t.data})},getPackages:function(){var a=this;this.packageLoading=!0,Object(i.g)({app_id:this.formData.app_id}).then(function(t){if(a.packageLoading=!1,a.packages=t.data,a.packages.length>0){var e=a.packages[0];a.packageChange(e.id)}})},getAppAmount:function(){var a=this;if(this.formData.package_id){this.tableLoading=!0;var t={type:1,app_id:this.formData.app_id,package_id:this.formData.package_id,month:12*this.year,activity_id:this.formData.activity_id};Object(i.c)(t).then(function(t){a.tableLoading=!1,a.amount=t.data.total_amount,a.cheapPrice=t.data.total_amount-t.data.total_preferential_amount}).catch(function(){a.tableLoading=!1})}else this.amount=0,this.cheapPrice=0}}},s={render:function(){var a=this,t=a.$createElement,e=a._self._c||t;return e("div",{staticClass:"app-container"},[e("el-steps",{attrs:{active:0,"finish-status":"success"}},[e("el-step",{attrs:{title:"确认订单"}}),a._v(" "),e("el-step",{attrs:{title:"选择支付方式"}}),a._v(" "),e("el-step",{attrs:{title:"支付成功"}})],1),a._v(" "),e("el-card",{directives:[{name:"loading",rawName:"v-loading",value:a.packageLoading,expression:"packageLoading"}],staticClass:"box-card"},[e("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[e("h1",[a._v("选择套餐配置")])]),a._v(" "),e("div",[e("el-radio-group",{model:{value:a.formData.package_id,callback:function(t){a.$set(a.formData,"package_id",t)},expression:"formData.package_id"}},a._l(a.packages,function(t,i){return e("el-radio",{key:i,attrs:{label:t.id},on:{change:a.packageChange}},[a._v(a._s(t.name))])}),1)],1)]),a._v(" "),e("el-card",{staticClass:"box-card",staticStyle:{"margin-top":"30px"}},[e("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[e("h3",[a._v("确认订单")])]),a._v(" "),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:a.tableLoading,expression:"tableLoading"}],staticStyle:{width:"100%"},attrs:{data:a.tableData,"highlight-current-row":""}},[e("el-table-column",{attrs:{fixed:"",prop:"title",label:"产品名称"},scopedSlots:a._u([{key:"default",fn:function(t){return[e("span",[a._v(a._s(t.row.name))])]}}])}),a._v(" "),e("el-table-column",{attrs:{label:"产品配置"},scopedSlots:a._u([{key:"default",fn:function(t){return[e("span",[a._v("渠道版(包年)")])]}}])}),a._v(" "),e("el-table-column",{attrs:{label:"购买方式"},scopedSlots:a._u([{key:"default",fn:function(t){return[e("span",[a._v("周期购买")])]}}])}),a._v(" "),e("el-table-column",{attrs:{label:"购买年限"},scopedSlots:a._u([{key:"default",fn:function(t){return[e("el-input-number",{attrs:{size:"mini","controls-position":"right",min:1,max:10,label:"年"},on:{change:a.getAppAmount},model:{value:a.year,callback:function(t){a.year=t},expression:"year"}})]}}])}),a._v(" "),e("el-table-column",{attrs:{label:"产品单价"},scopedSlots:a._u([{key:"default",fn:function(t){return[e("span",{staticStyle:{color:"firebrick"}},[a._v("￥"+a._s(t.row.package.price))])]}}])})],1)],1),a._v(" "),e("el-card",{staticClass:"box-card",staticStyle:{"margin-top":"30px"}},[e("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[e("h1",[a._v("备注")])]),a._v(" "),e("el-input",{attrs:{type:"textarea",rows:3,maxlength:"200","show-word-limit":"",placeholder:"请填写订单备注，限200字"},model:{value:a.formData.remark,callback:function(t){a.$set(a.formData,"remark",t)},expression:"formData.remark"}})],1),a._v(" "),e("el-card",{staticClass:"box-card",staticStyle:{"margin-top":"30px"}},[e("div",{staticClass:"view-price"},[e("span",[a._v("￥"+a._s(a.amount))]),a._v(" "),e("el-button",{attrs:{loading:a.uploadLoading},on:{click:function(t){return a.commit()}}},[a._v("我已接受协议,前往支付")])],1)]),a._v(" "),a._m(0),a._v(" "),e("el-card",{staticClass:"box-card",staticStyle:{"margin-top":"30px"}},[e("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[e("h1",[a._v("温馨提示")])]),a._v(" "),e("div",[e("span",[a._v("1.为了让服务商更好的服务您，下单后系统将当前账号下的联系方式传递给服务商，如果您不想提供，可以勾选一下选项；")])])])],1)},staticRenderFns:[function(){var a=this.$createElement,t=this._self._c||a;return t("div",{staticClass:"text1"},[t("span",[this._v("点击以上按钮，代表您已阅读并同意")]),this._v(" "),t("a",{attrs:{href:"/static/license/buy.html",target:"_blank"}},[this._v("《云市场平台服务协议》")])])}]};var o=e("VU/8")(n,s,!1,function(a){e("xTZi")},"data-v-258b07ba",null);t.default=o.exports},xTZi:function(a,t){}});