<!DOCTYPE html><html><head><meta charset=utf-8><meta name=viewport content="width=device-width,intial-scale=0,maximum-scale=0,user-scalable=yes,shrink-to-fit=no"><title>资海云-企业AI数字化平台，系统，办公系统，CRM软件，监控，考勤，小程序开发，网站制作，SaaS系统，智能印章，考勤打卡系统，人力资源系统，AIGC</title><meta name=keywords content=系统，办公系统，CRM软件，监控，考勤，小程序开发，网站制作，SaaS系统，智能印章，考勤打卡系统，人力资源系统，人力资源管理系统，移动办公，OA办公管理，HRIS，自助建站，一站式建站平台，站群系统，网站建设、网站制作代理加盟、企业网站建设，响应式网站建设，一站式SaaS建站解决方案，高端网站建设，网络营销，AIGC，企业AI数字化平台><meta name=description content=系统，办公系统，CRM软件，监控，考勤，小程序开发，网站制作，SaaS系统，智能印章，考勤打卡系统，人力资源系统，人力资源管理系统，移动办公，OA办公管理，HRIS，自助建站，一站式建站平台，AIGC，企业AI数字化平台><link rel=icon href=static/logo.png><script>/* 如果是ie浏览器 */
      if (!!window.ActiveXObject || "ActiveXObject" in window) {
        window.location.href = "/ie.html";
      }</script><script type=text/javascript src="https://webapi.amap.com/maps?v=1.4.15&key=e4929c0280c3c0e563c13e1f0c817bc9&plugin=AMap.Autocomplete,AMap.PlaceSearch,AMap.Geocoder"></script><style>* {
            margin: 0;
        }

        #newBridge .nb-nodeboard-left-bottom {
            right: 0 !important;
            left: auto !important;
        }

        #zhyConsoleLeftSide {
            position: absolute !important;
            top: 0;
            bottom: 0;
        }

        @keyframes pulse {
            0% {
                -webkit-transform: scale3d(1, 1, 1);
                transform: scale3d(1, 1, 1);
            }

            50% {
                -webkit-transform: scale3d(1.05, 1.05, 1.05);
                transform: scale3d(1.05, 1.05, 1.05);
            }

            100% {
                -webkit-transform: scale3d(1, 1, 1);
                transform: scale3d(1, 1, 1);
            }
        }

        .pulse {
            -webkit-animation-name: pulse;
            animation-name: pulse;
            animation-duration: 0.5s;
        }

        .el-notification, .el-message {
            z-index: 999999999 !important;
        }

        body .el-popconfirm__action{
            margin-top: 10px;
        }
        html{
            scroll-behavior: smooth;
        }</style><script src=/static/js/jq.js></script><script src=/static/js/common/cookies.js></script><script>const hostMap = {
        "localhost:8080": "local",
        "localhost:8081": "local",
        "**********:8080": "local",
        "**********:8081": "local",
        "**********:8080": "local",
        "**********:8081": "local",
        "***********:8080": "local",
        "***********:8080": "local",
        "www.china9.cn": "pro",
        "china9.cn": "pro",
        "dev.china9.cn": "dev"
      }
      var environment = hostMap[window.location.host] || "pro";</script><script>var _hmt = _hmt || [];
      var url = window.location.href;
      if (environment !== "local") {
        if (url.indexOf("https") < 0) {
          url = url.replace("http:", "https:");
          window.location.replace(url);
        }
      }</script><script src=/static/js/esdk-obs-browserjs-without-polyfill-3.19.9.min.js></script><link href=./static/css/app.dd715def0610af5d62d1b769868bcdad.css rel=stylesheet></head><body><div id=app></div><script type=text/javascript src=./static/js/manifest.e6ce09437e4fc28c76b0.js></script><script type=text/javascript src=./static/js/vendor.fd9f8acef07488f56c4f.js></script><script type=text/javascript src=./static/js/app.219ad344bbaa244a82b2.js></script></body><script src=/static/js/swiper.animate1.0.3.min.js></script></html>