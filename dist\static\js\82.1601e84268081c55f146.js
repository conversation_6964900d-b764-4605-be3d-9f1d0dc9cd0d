webpackJsonp([82],{CLp1:function(e,t){},v6wK:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var l=a("cMGX"),o=a("vLgD");function r(e){return Object(o.a)({url:"/api/orders/books",method:"POST",data:e})}var n={name:"waterBills",components:{Pagination:l.a},data:function(){return{tableLoading:!1,excelLoading:!1,formQuery:{page:1,perPage:10},tableData:[],total:0,json_fields:{"流水单号":"order_no","类型":"type","内容":"content","金额":"amount","创建时间":"created_at"},excelAllData:[]}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.tableLoading=!0,r(this.formQuery).then(function(t){e.tableLoading=!1,e.total=t.data.total,e.tableData=t.data.data}).catch(function(){e.tableLoading=!1})},excelOut:function(){var e=this;this.formQuery.all=1,this.excelLoading=!0,r(this.formQuery).then(function(t){e.excelAllData=t.data,e.handleDownload(),e.excelLoading=!1}).catch(function(){e.excelLoading=!1})},handleDownload:function(){var e=this;a.e(109).then(a.bind(null,"zWO4")).then(function(t){var a=e.formatJson(["order_no","type","content","amount","created_at"],e.excelAllData);t.export_json_to_excel({header:["流水单号","类型","内容","金额","创建时间"],data:a,filename:"流水订单",autoWidth:!0,bookType:"xlsx"})})},formatJson:function(e,t){return t.map(function(t){return e.map(function(e){return t[e]})})}}},i={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[e._v("流水账单")])]),e._v(" "),a("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:e.formQuery,size:"small"}},[a("el-form-item",{attrs:{label:"类型"}},[a("el-select",{attrs:{clearable:""},model:{value:e.formQuery.type,callback:function(t){e.$set(e.formQuery,"type",t)},expression:"formQuery.type"}},[a("el-option",{attrs:{label:"消费",value:1}}),e._v(" "),a("el-option",{attrs:{label:"提现",value:2}}),e._v(" "),a("el-option",{attrs:{label:"充值",value:3}})],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"日期"}},[a("el-date-picker",{attrs:{type:"month",format:"yyyy-M","value-format":"yyyy-M",placeholder:"选择月"},model:{value:e.formQuery.time,callback:function(t){e.$set(e.formQuery,"time",t)},expression:"formQuery.time"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"单号"}},[a("el-input",{attrs:{placeholder:"请输入流水单号",clearable:""},model:{value:e.formQuery.order_no,callback:function(t){e.$set(e.formQuery,"order_no",t)},expression:"formQuery.order_no"}})],1),e._v(" "),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.getList}},[e._v("搜索")])],1),e._v(" "),a("el-form-item",[a("el-button",{attrs:{type:"warning",size:"small",loading:e.excelLoading},on:{click:e.excelOut}},[e._v("导出")])],1)],1),e._v(" "),a("el-divider"),e._v(" "),a("div",{staticClass:"table"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.tableLoading,expression:"tableLoading"}],staticStyle:{width:"100%"},attrs:{data:e.tableData,stripe:""}},[a("el-table-column",{attrs:{prop:"order_no",label:"流水单号",width:"300"}}),e._v(" "),a("el-table-column",{attrs:{prop:"type",label:"类型",width:"150"}}),e._v(" "),a("el-table-column",{attrs:{prop:"content",label:"内容"}}),e._v(" "),a("el-table-column",{attrs:{prop:"amount",label:"金额"}}),e._v(" "),a("el-table-column",{attrs:{prop:"created_at",label:"创建时间"}})],1)],1),e._v(" "),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],staticStyle:{"text-align":"center"},attrs:{total:e.total,page:e.formQuery.page,limit:e.formQuery.perPage},on:{"update:page":function(t){return e.$set(e.formQuery,"page",t)},"update:limit":function(t){return e.$set(e.formQuery,"perPage",t)},pagination:e.getList}})],1)},staticRenderFns:[]};var s=a("VU/8")(n,i,!1,function(e){a("CLp1")},"data-v-596b6374",null);t.default=s.exports}});