webpackJsonp([87],{WixY:function(t,a,e){"use strict";Object.defineProperty(a,"__esModule",{value:!0});var i=e("pI5c"),s={name:"sureOrder",data:function(){return{no:this.$route.query.no,tableLoading:!1,uploadLoading:!1,tableData:[],amount:0,cheapPrice:0,year:1,data:{},item:{}}},created:function(){this.item=this.$store.state.temp,this.data={order_type:2,type:"1",app_id:this.item.app_id,package_id:this.item.package_id,month:12*this.year,activity_id:"",source:"pc",original_order_no:this.no,remark:""},this.tableData=[this.item],this.getAppAmount()},methods:{commit:function(){var t=this;this.uploadLoading=!0,Object(i._14)(this.data).then(function(a){t.uploadLoading=!1,t.$router.push({path:"/pay",query:{no:a.data.order_no}})}).catch(function(){t.uploadLoading=!1})},getAppAmount:function(){this.data.month=12*this.year,this.amount=this.year*this.item.renew_price}}},r={render:function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",{staticClass:"app-container"},[e("el-steps",{attrs:{active:0,"finish-status":"success"}},[e("el-step",{attrs:{title:"确认订单"}}),t._v(" "),e("el-step",{attrs:{title:"选择支付方式"}}),t._v(" "),e("el-step",{attrs:{title:"支付成功"}})],1),t._v(" "),e("el-card",{staticClass:"box-card"},[e("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[e("h3",[t._v("确认订单")])]),t._v(" "),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.tableLoading,expression:"tableLoading"}],staticStyle:{width:"100%"},attrs:{data:t.tableData,"highlight-current-row":""}},[e("el-table-column",{attrs:{fixed:"",prop:"name",label:"产品名称"}}),t._v(" "),e("el-table-column",{attrs:{prop:"package",label:"产品配置"}}),t._v(" "),e("el-table-column",{attrs:{label:"购买年限"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-input-number",{attrs:{size:"mini","controls-position":"right",min:1,max:10,label:"年"},on:{change:t.getAppAmount},model:{value:t.year,callback:function(a){t.year=a},expression:"year"}})]}}])}),t._v(" "),e("el-table-column",{attrs:{label:"续费价格"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("span",{staticStyle:{color:"firebrick"}},[t._v("￥"+t._s(a.row.renew_price))])]}}])})],1)],1),t._v(" "),e("el-card",{staticClass:"box-card",staticStyle:{"margin-top":"30px"}},[e("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[e("h1",[t._v("备注")])]),t._v(" "),e("el-input",{attrs:{type:"textarea",rows:3,maxlength:"200","show-word-limit":"",placeholder:"请填写订单备注，限200字"},model:{value:t.data.remark,callback:function(a){t.$set(t.data,"remark",a)},expression:"data.remark"}})],1),t._v(" "),e("el-card",{staticClass:"box-card",staticStyle:{"margin-top":"30px"}},[e("div",{staticClass:"view-price"},[e("span",[t._v("￥"+t._s(t.amount))]),t._v(" "),e("el-button",{attrs:{loading:t.uploadLoading},on:{click:function(a){return t.commit()}}},[t._v("我已接受协议,前往支付")])],1)]),t._v(" "),t._m(0),t._v(" "),e("el-card",{staticClass:"box-card",staticStyle:{"margin-top":"30px"}},[e("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[e("h1",[t._v("温馨提示")])]),t._v(" "),e("div",[e("span",[t._v("1.为了让服务商更好的服务您，下单后系统将当前账号下的联系方式传递给服务商，如果您不想提供，可以勾选一下选项；")])])])],1)},staticRenderFns:[function(){var t=this.$createElement,a=this._self._c||t;return a("div",{staticClass:"text1"},[a("span",[this._v("点击以上按钮，代表您已阅读并同意")]),this._v(" "),a("a",[this._v("《云市场平台服务协议》")])])}]};var n=e("VU/8")(s,r,!1,function(t){e("xnQr")},"data-v-3e2d1f9a",null);a.default=n.exports},xnQr:function(t,a){}});