webpackJsonp([105],{"6adp":function(t,e){},RyUF:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=a("cMGX"),s=a("pI5c"),i={name:"staffReview",components:{Pagination:n.a},data:function(){return{tableLoading:!1,tableData:[],total:0,queryList:{page:1,perPage:10,limit:10,status:0},selectData:[]}},created:function(){this.getList()},methods:{clickType:function(t){this.queryList.status=t,this.queryList.page=1,this.getList()},getList:function(){var t=this;this.tableLoading=!0,Object(s._28)(this.queryList).then(function(e){t.tableLoading=!1,t.tableData=e.data.data,t.total=e.data.total}).catch(function(){t.tableLoading=!1})},auth:function(t,e){var a=this;this.$confirm("确定您的操作么, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){Object(s._27)({id:t.id,status:e,remark:1===e?"通过":"拒绝"}).then(function(n){a.$message.success("操作成功"),t.status=e}).catch(function(){})}).catch(function(){})}}},l={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-container"},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[t._v("员工申请加入列表")]),t._v(" "),a("el-button",{staticStyle:{float:"right"},attrs:{type:"text"},on:{click:t.getList}},[t._v("刷新")])],1),t._v(" "),a("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:t.queryList,size:"small"}},[a("el-form-item",[a("el-button",{attrs:{type:0===t.queryList.status?"primary":""},on:{click:function(e){return t.clickType(0)}}},[t._v("待加入")]),t._v(" "),a("el-button",{attrs:{type:1===t.queryList.status?"primary":""},on:{click:function(e){return t.clickType(1)}}},[t._v("已通过")]),t._v(" "),a("el-button",{attrs:{type:-1===t.queryList.status?"primary":""},on:{click:function(e){return t.clickType(-1)}}},[t._v("已驳回")])],1)],1),t._v(" "),a("el-divider"),t._v(" "),a("div",{staticClass:"table",staticStyle:{"margin-top":"50px"}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.tableLoading,expression:"tableLoading"}],staticStyle:{width:"100%"},attrs:{data:t.tableData,stripe:""},on:{"selection-change":t.handleSelectionChange}},[a("el-table-column",{attrs:{label:"姓名",prop:"user.name"}}),t._v(" "),a("el-table-column",{attrs:{label:"手机号",prop:"user.phone"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(t._f("phoneEncryption")(e.row.user.phone)))])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"性别"},scopedSlots:t._u([{key:"default",fn:function(e){return[0===e.row.user.sex?a("span",{staticStyle:{color:"#373D41"}},[t._v("未知")]):t._e(),t._v(" "),1===e.row.user.sex?a("span",{staticStyle:{color:"#3a8ee6"}},[t._v("男")]):t._e(),t._v(" "),2===e.row.user.sex?a("span",{staticStyle:{color:"#ff4d51"}},[t._v("女")]):t._e()]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"理由",prop:"reason"}}),t._v(" "),a("el-table-column",{attrs:{label:"申请时间",prop:"created_at"}}),t._v(" "),a("el-table-column",{attrs:{label:"操作"},scopedSlots:t._u([{key:"default",fn:function(e){return 0===e.row.status?[a("el-button",{staticStyle:{color:"#3a8ee6"},attrs:{type:"text"},on:{click:function(a){return t.auth(e.row,1)}}},[t._v("通过")]),t._v(" "),a("el-button",{staticStyle:{color:"#ff4d51"},attrs:{type:"text"},on:{click:function(a){return t.auth(e.row,-1)}}},[t._v("拒绝")])]:void 0}}],null,!0)})],1)],1),t._v(" "),a("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total>0"}],staticStyle:{"text-align":"center"},attrs:{total:t.total,page:t.queryList.page,limit:t.queryList.perPage},on:{"update:page":function(e){return t.$set(t.queryList,"page",e)},"update:limit":function(e){return t.$set(t.queryList,"perPage",e)},pagination:t.getList}})],1)],1)},staticRenderFns:[]};var r=a("VU/8")(i,l,!1,function(t){a("6adp")},"data-v-01bfc481",null);e.default=r.exports}});