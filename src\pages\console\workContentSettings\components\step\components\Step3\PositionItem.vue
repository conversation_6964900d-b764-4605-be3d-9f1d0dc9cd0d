<template>
  <div class="work-content-box relative">
    <div :class="['position-wrapper', { 'is-expanded': expandedKey === `${rowIndex}-${contentIndex}` }]">
      <div class="top-wrapper flex-justify-between flex-align-center">
        <div class="position-title flex-x text-ellipsis" @click="$emit('expand', contentIndex)">
          <img src="@/assets/image/console/work/matter.png" alt="">
          <el-tooltip :content="content.name">
            <span>{{ content.name }}</span>
          </el-tooltip>
        </div>
        <div class="action-wrap flex-x">
          <span><img src="@/assets/image/console/work/edit.png" alt="" @click="handleEditWorkContent('edit')"></span>
        </div>
      </div>
      <div class="position-content" v-if="content.qurstjobcons && content.aick">
        <WorkTags :list="content.qurstjobcons" :show-add="true" :show-close="true" :showAutoCreateBtn="true" @add="name => handleAddContents(name, content)" @remove="handleRemoveContents" @edit="data => handleEditContents(data, content)" @aiCreate="handleAICreate" :ai-create-loading="aiCreateLoading"/>
        <!--<div class="task-item" v-for="(task, taskIndex) in content.qurstjobcons" :key="taskIndex">
          <span class="task-name">{{ task.name }}</span>
        </div>-->
      </div>
      <el-empty v-else :image="require('@/assets/image/console/empty.png')" :image-size="100" style="padding: 10px;" description="正在生成中..." />
      <div class="more flex flex-align-center flex-justify-center" v-if="content.aick ? content.qurstjobcons && content.qurstjobcons.length > 2 : false">
        <el-button type="text" @click="toggleExpand">
          <span>{{ expandedKey === `${rowIndex}-${contentIndex}` ? '收起' : `查看全部${content.qurstjobcons ? content.qurstjobcons.length : 0}项工作任务` }}</span>
          <img
              :src="require('@/assets/image/console/work/arrow2.png')"
              alt=""
              :class="{ 'is-expanded': expandedKey === `${rowIndex}-${contentIndex}` }"
          >
        </el-button>
      </div>
    </div>

    <el-dialog :title="title" :visible.sync="showEditDialog" :close-on-click-modal="false" width="400px">
      <el-form :model="editForm" label-position="top">
        <el-form-item label="工作内容">
          <el-input v-model="editForm.positionName" placeholder="工作内容"></el-input>
        </el-form-item>
        <!--<el-form-item label="工作任务">
          <WorkTags :list="editForm.list" :show-add="true" :show-close="true" @add="handleAddContents" @remove="handleRemoveContents" @edit="handleEditContents"/>
        </el-form-item>-->
      </el-form>
      <div slot="footer" class="dialog-footer flex flex-justify-center flex-align-center">
        <el-button @click="cancelEdit">取 消</el-button>
        <el-button type="primary" @click="saveEdit" :loading="editLoading">保存修改</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import WorkTags from '../WorkTags.vue'
import {
  recreatTaskApi,
  editWorkContent,
  workTaskAddApi, workTaskDeleteApi, workTaskUpdateApi
} from '../../../../../../../api/newTask'

export default {
  name: 'PositionItem',
  components: {WorkTags},
  inject: ['activeRow', 'setActiveRow', 'expandedCard', 'setExpandedCard'],
  props: {
    content: {
      type: Object,
      required: true
    },
    contentIndex: {
      type: Number,
      required: true
    },
    workContent: {
      type: Object,
      required: true
    },
    rowIndex: {
      type: Number,
      required: true
    },
    expandedKey: String
  },
  data () {
    return {
      showEditDialog: false,
      editForm: {
        name: '',
        list: []
      },
      editLoading: false,
      title: '编辑工作内容',
      aiCreateLoading: false
    }
  },
  computed: {
    activeRowVal () {
      return this.activeRow()
    }
  },
  methods: {
    toggleExpand () {
      const key = `${this.rowIndex}-${this.contentIndex}`
      this.$emit('toggle-expand', key)
    },
    async handleRemoveContents (item) {
      if (!item || !item.id) return
      const res = await workTaskDeleteApi({ id: item.id })
      if (res.code === 200) {
        this.$message.success('删除成功')
      } else {
        this.$message.error(res.msg || res.message || '删除失败')
      }
    },
    async handleAddContents (content, workContent) {
      if (!content || !workContent || !workContent.id) return
      try {
        const res = await workTaskAddApi({
          jobcontype: workContent.id,
          name: content,
          gw_id: this.workContent.id
        })
        if (res.code === 200) {
          this.$message.success('添加成功')
          workContent.qurstjobcons[0].id = res.data.id
        } else {
          this.$message.error(res.msg || res.message || '添加失败')
          //   删掉新增的
          workContent.qurstjobcons.splice(0, 1)
        }
      } catch (error) {
        this.$message.error('添加失败')
        //   删掉新增的
        workContent.qurstjobcons.splice(0, 1)
      }
    },
    async handleEditContents (item) {
      if (!item.id || !item.name) return
      try {
        const res = await workTaskUpdateApi({
          id: item.id,
          name: item.name
        })
        if (res.code === 200) {
          this.$message.success('修改成功')
        } else {
          this.$message.error(res.msg || res.message || '修改失败')
        }
      } catch (error) {
        this.$message.error('修改失败')
      }
    },
    cancelEdit () {
      this.showEditDialog = false
    },
    async saveEdit () {
      try {
        this.editLoading = true
        const res = await editWorkContent({
          name: this.editForm.positionName,
          id: this.editForm.id,
          type: 1
        })
        if (res.code === 200) {
          this.$message.success('修改成功')
        } else {
          this.$message.error(res.message)
        }
      } catch (error) {
        this.$message.error(error.message)
      } finally {
        // 更新岗位信息逻辑
        this.$set(this.workContent.list, this.contentIndex, {
          name: this.editForm.positionName,
          id: this.editForm.id,
          qurstjobcons: this.editForm.list
        })
        this.editLoading = false
        this.showEditDialog = false
      }
    },
    async handleEditWorkContent (type) {
      this.showEditDialog = true
      this.editForm = {
        id: this.content.id,
        positionName: this.content.name,
        list: this.content.qurstjobcons
      }
    },
    async handleAICreate () {
      if (!this.workContent.id || !this.content.id) return
      try {
        this.aiCreateLoading = true
        const res = await recreatTaskApi({
          id: this.content.id,
          gw_id: this.workContent.id
        })
        if (res.code === 200) {
          this.$message.success('创建成功')
          this.$emit('refresh', false)
        } else {
          this.$message.error(res.msg || res.message || '创建失败')
        }
      } catch (e) {
        console.log(e)
        this.$message.error('创建失败')
      } finally {
        this.aiCreateLoading = false
      }
    }
  }
}
</script>

<style scoped lang="scss">
/deep/.el-empty {
  .el-empty__description {
    margin-top: 0px;
  }
}
.flex-x {
  display: flex;
  align-items: center;
}
.position-wrapper {
  background: #FFFFFF;
  border-radius: 4px;
  border: 1px solid #E2E6EC;
  padding: 12px 14px;
  height: 145px;
  overflow: hidden;
  position: relative;
  &.is-expanded {
    z-index: 10;
    position: absolute;
    left: 0;
    right: 0;
    background: #fff;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    height: auto !important;
    min-height: 230px;
    cursor: default;
    .position-content{
      padding-bottom: 30px;
    }
  }
  .more{
    background: #fff;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 30px;
    padding: 5px 0;
    /deep/ .el-button{
      font-size: 14px;
      color: #999999;
    }
  }
  .top-wrapper {
    justify-content: space-between;
    .position-title {
      img {
        width: 15px;
        height: 15px;
        margin-right: 6px;
      }
      span {
        font-size: 16px;
        color: #000000;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
    .action-wrap {
      img {
        width: 14px;
        height: 14px;
        margin-left: 10px;
        cursor: pointer;
      }
    }
  }
  .position-content {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    /deep/ .ai-agent-add-btn,
    /deep/ .input-new-tag{
      height: 32px;
      line-height: 32px;
    }
    /deep/ .ai-agent-tag {
      padding: 0 12px;
      height: 32px;
      line-height: 32px;
      background: #F2F6F9;
      border-radius: 2px;
      border: 1px solid #E6E9F0;
      margin-right: 10px;
      cursor: pointer;
      .task-name {
        font-size: 14px;
        color: #555555;
      }
    }
  }
}

.dialog-footer{
  display: flex;
  justify-content: center;
  align-items: center;
  /deep/ .el-button {
    width: 100px;
    height: 38px;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0;
    border-radius: 4px;
    font-size: 16px;
    &.el-button--primary {
      border: var(--el-color-primary);
      background: var(--el-color-primary);
    }
  }
}

/deep/ .el-dialog__body {
  padding: 20px;
}

/deep/ .el-form-item__label{
  font-size: 16px;
  color: #555555;
  font-weight: normal;
}
</style>
