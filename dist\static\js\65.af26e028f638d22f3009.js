webpackJsonp([65],{"5bDt":function(t,e){},qTps:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=a("lbHh"),l=a.n(n),i="shopCar";function o(){return l.a.get(i)?JSON.parse(l.a.get(i)):[]}function c(t){return l.a.set(i,t)}var r=a("pI5c"),s={name:"shopcar",data:function(){return{tableLoading:!1,tableData:[],amount:0,cheapPrice:0,allChecked:!1}},created:function(){var t,e,a,n=this.$route.query;n&&n.app_id&&n.package_id&&n.title&&(t={app_id:n.app_id,package_id:n.package_id,activity:n.activity,title:n.title,config:"渠道版(包年)",type:"周期购买",year:1,price:.01,checked:!1},e=o()?o():[],a=!0,e.forEach(function(e){console.log(e),e.app_id===t.app_id&&(a=!1)}),a&&(e.unshift(t),l.a.set(i,e))),this.tableData=o()?o():[],console.log(this.tableData),this.getAppAmount()},methods:{allCheckedChange:function(t){this.tableData.forEach(function(e,a,n){e.checked=t}),this.getAppAmount()},clear:function(){if(!0===this.allChecked)c([]),this.tableData=[];else{var t=[];this.tableData.forEach(function(e,a,n){!1===e.checked&&t.push(e)}),this.tableData=t,c(this.tableData)}},getAppAmount:function(){var t=this,e=[];this.tableData.forEach(function(t){if(!0===t.checked){var a={type:"1",app_id:t.app_id,package_id:t.package_id,month:12*t.year,activity_id:t.activity_id};e.push(a)}}),e.length>0?(this.tableLoading=!0,Object(r.c)({list_goods:e}).then(function(e){t.tableLoading=!1,t.amount=e.data.total_amount,t.cheapPrice=e.data.total_amount-e.data.total_preferential_amount}).catch(function(){t.tableLoading=!1})):(this.amount=0,this.cheapPrice=0)},push:function(){c(this.tableData),this.$router.push({name:"SureOrder"})},deleteHandle:function(t){this.tableData.splice(t,1),this.getAppAmount()}}},u={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-container"},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("i",{staticClass:"iconfont icongouwuche"}),t._v(" "),a("span",[t._v("购物车")])]),t._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.tableLoading,expression:"tableLoading"}],staticStyle:{width:"100%"},attrs:{data:t.tableData,"highlight-current-row":""}},[a("el-table-column",{attrs:{fixed:"",prop:"title",label:"产品名称"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-checkbox",{on:{change:t.getAppAmount},model:{value:e.row.checked,callback:function(a){t.$set(e.row,"checked",a)},expression:"scope.row.checked"}}),t._v(" "),a("span",[t._v(t._s(e.row.title))])]}}])}),t._v(" "),a("el-table-column",{attrs:{prop:"config",label:"产品配置"}}),t._v(" "),a("el-table-column",{attrs:{prop:"type",label:"购买方式"}}),t._v(" "),a("el-table-column",{attrs:{label:"购买年限"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-input-number",{attrs:{size:"mini","controls-position":"right",min:1,max:10,label:"年"},on:{change:t.getAppAmount},model:{value:e.row.year,callback:function(a){t.$set(e.row,"year",a)},expression:"scope.row.year"}})]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"购买价格"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",{staticStyle:{color:"firebrick"}},[t._v("￥"+t._s(e.row.price))])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"操作",fixed:"right",width:"150"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-popconfirm",{attrs:{title:"确定删除当前产品吗？"},on:{onConfirm:function(a){return t.deleteHandle(e.row.index)}}},[a("el-button",{attrs:{slot:"reference",type:"text"},slot:"reference"},[t._v("删除")])],1)]}}])})],1),t._v(" "),t.tableData.length>0?a("el-row",[a("el-col",[a("div",{staticClass:"footer"},[a("el-checkbox",{on:{change:t.allCheckedChange},model:{value:t.allChecked,callback:function(e){t.allChecked=e},expression:"allChecked"}},[t._v("已选")]),t._v(" "),a("a",{on:{click:t.clear}},[t._v("批量删除")]),t._v(" "),a("div",{staticClass:"text"},[a("div",[a("span",[t._v("总配置费用:")]),t._v(" "),a("span",[t._v("￥"+t._s(t.amount))])]),t._v(" "),a("div",[a("span",[t._v("产品优惠:")]),t._v(" "),a("span",[t._v("￥"+t._s(t.cheapPrice))])])]),t._v(" "),a("el-button",{on:{click:function(e){return t.push("/sureOrder")}}},[t._v("立即购买")])],1)])],1):t._e()],1)],1)},staticRenderFns:[]};var p=a("VU/8")(s,u,!1,function(t){a("5bDt")},"data-v-96feb546",null);e.default=p.exports}});